package co.com.gedsys.proezedure.dto;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.TemplateStatus;
import co.com.gedsys.proezedure.util.JsonConverter;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.UUID;

public class ProcessDefinitionResponse {

    private UUID id;
    private String name;
    private String description;
    private String version;
    private LinkedHashMap<String, Object> defaultVariables;
    private LocalDateTime createdAt;
    private TemplateStatus status;

    public ProcessDefinitionResponse() {
    }

    public ProcessDefinitionResponse(ProcessDefinition definition) {
        this.id = definition.getId();
        this.name = definition.getName();
        this.description = definition.getDescription();
        this.version = definition.getVersion();
        this.createdAt = definition.getCreatedAt();
        this.status = definition.getStatus();
        // defaultVariables se establecerá usando el método factory
    }

    public static ProcessDefinitionResponse from(ProcessDefinition definition, JsonConverter jsonConverter) {
        ProcessDefinitionResponse response = new ProcessDefinitionResponse();
        response.id = definition.getId();
        response.name = definition.getName();
        response.description = definition.getDescription();
        response.version = definition.getVersion();
        response.defaultVariables = jsonConverter.jsonToMapOrEmpty(definition.getDefaultVariables());
        response.createdAt = definition.getCreatedAt();
        response.status = definition.getStatus();
        return response;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public LinkedHashMap<String, Object> getDefaultVariables() {
        return defaultVariables;
    }

    public void setDefaultVariables(LinkedHashMap<String, Object> defaultVariables) {
        this.defaultVariables = defaultVariables;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public TemplateStatus getStatus() {
        return status;
    }

    public void setStatus(TemplateStatus status) {
        this.status = status;
    }
}
