package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;
import co.com.gedsys.proezedure.exception.*;
import co.com.gedsys.proezedure.repository.SubtaskRepository;
import co.com.gedsys.proezedure.repository.TaskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskServiceValidationTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private SubtaskRepository subtaskRepository;

    @Mock
    private VariableService variableService;

    @Mock
    private ValidationService validationService;

    @InjectMocks
    private TaskService taskService;

    private Task testTask;
    private UUID taskId;
    private UUID processInstanceId;

    @BeforeEach
    void setUp() {
        taskId = UUID.randomUUID();
        processInstanceId = UUID.randomUUID();
        
        testTask = new Task(processInstanceId, "Test Task", "Description", "form-key-1", 1, "{}");
        testTask.setId(taskId);
        testTask.setStatus(TaskStatus.ASSIGNED);
        testTask.setAssignedTo("jmarin");
    }

    @Test
    void shouldThrowExceptionWhenCreatingTaskWithInvalidProcessInstance() {
        // Given
        doThrow(new ReferentialIntegrityException("Task", null, "ProcessInstance", processInstanceId))
                .when(validationService).validateProcessInstanceExists(processInstanceId);

        // When & Then
        assertThatThrownBy(() -> taskService.create(processInstanceId, "Test", "Desc", "form-key", 1, "{}"))
                .isInstanceOf(ReferentialIntegrityException.class)
                .hasMessageContaining("ProcessInstance");

        verify(validationService).validateProcessInstanceExists(processInstanceId);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenCreatingTaskWithDuplicateFormKey() {
        // Given
        doThrow(new DuplicateFormKeyException("form-key", "Task", processInstanceId))
                .when(validationService).validateTaskFormKeyUnique("form-key", processInstanceId, null);

        // When & Then
        assertThatThrownBy(() -> taskService.create(processInstanceId, "Test", "Desc", "form-key", 1, "{}"))
                .isInstanceOf(DuplicateFormKeyException.class)
                .hasMessageContaining("form-key");

        verify(validationService).validateTaskFormKeyUnique("form-key", processInstanceId, null);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenCreatingTaskWithDuplicateOrder() {
        // Given
        doThrow(new InvalidOrderSequenceException(1, "Task", processInstanceId))
                .when(validationService).validateTaskOrderUnique(1, processInstanceId, null);

        // When & Then
        assertThatThrownBy(() -> taskService.create(processInstanceId, "Test", "Desc", "form-key", 1, "{}"))
                .isInstanceOf(InvalidOrderSequenceException.class)
                .hasMessageContaining("order sequence");

        verify(validationService).validateTaskOrderUnique(1, processInstanceId, null);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenCreatingTaskWithInvalidJson() {
        // Given
        doThrow(new JsonSchemaValidationException("inputs", "valid JSON", "{invalid}"))
                .when(validationService).validateJsonStructure("{invalid}", "inputs");

        // When & Then
        assertThatThrownBy(() -> taskService.create(processInstanceId, "Test", "Desc", "form-key", 1, "{invalid}"))
                .isInstanceOf(JsonSchemaValidationException.class)
                .hasMessageContaining("inputs");

        verify(validationService).validateJsonStructure("{invalid}", "inputs");
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenCompletingTaskWithWrongUser() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new TaskNotAssignedException(taskId, "wronguser"))
                .when(validationService).validateUserPermission(testTask, "wronguser");

        // When & Then
        assertThatThrownBy(() -> taskService.complete(taskId, "wronguser", "{}"))
                .isInstanceOf(TaskNotAssignedException.class)
                .hasMessageContaining("wronguser");

        verify(validationService).validateUserPermission(testTask, "wronguser");
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenCompletingTaskWithInvalidStateTransition() {
        // Given
        testTask.setStatus(TaskStatus.COMPLETED);
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new InvalidStateTransitionException("COMPLETED", "COMPLETED", "Task"))
                .when(validationService).validateTaskStateTransition(TaskStatus.COMPLETED, TaskStatus.COMPLETED);

        // When & Then
        assertThatThrownBy(() -> taskService.complete(taskId, "jmarin", "{}"))
                .isInstanceOf(InvalidStateTransitionException.class)
                .hasMessageContaining("COMPLETED");

        verify(validationService).validateTaskStateTransition(TaskStatus.COMPLETED, TaskStatus.COMPLETED);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenCompletingTaskWithIncompleteSubtasks() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new TaskCannotBeCompletedException(taskId))
                .when(validationService).validateAllSubtasksCompleted(taskId);

        // When & Then
        assertThatThrownBy(() -> taskService.complete(taskId, "jmarin", "{}"))
                .isInstanceOf(TaskCannotBeCompletedException.class)
                .hasMessageContaining(taskId.toString());

        verify(validationService).validateAllSubtasksCompleted(taskId);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenCompletingTaskWithInvalidOutputJson() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new JsonSchemaValidationException("outputs", "valid JSON", "{invalid}"))
                .when(validationService).validateJsonStructure("{invalid}", "outputs");

        // When & Then
        assertThatThrownBy(() -> taskService.complete(taskId, "jmarin", "{invalid}"))
                .isInstanceOf(JsonSchemaValidationException.class)
                .hasMessageContaining("outputs");

        verify(validationService).validateJsonStructure("{invalid}", "outputs");
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenUpdatingTaskStatusWithInvalidTransition() {
        // Given
        testTask.setStatus(TaskStatus.COMPLETED);
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new InvalidStateTransitionException("COMPLETED", "IN_PROGRESS", "Task"))
                .when(validationService).validateTaskStateTransition(TaskStatus.COMPLETED, TaskStatus.IN_PROGRESS);

        // When & Then
        assertThatThrownBy(() -> taskService.updateStatus(taskId, TaskStatus.IN_PROGRESS))
                .isInstanceOf(InvalidStateTransitionException.class)
                .hasMessageContaining("COMPLETED")
                .hasMessageContaining("IN_PROGRESS");

        verify(validationService).validateTaskStateTransition(TaskStatus.COMPLETED, TaskStatus.IN_PROGRESS);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenUpdatingTaskWithDuplicateFormKey() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new DuplicateFormKeyException("new-form-key", "Task", processInstanceId))
                .when(validationService).validateTaskFormKeyUnique("new-form-key", processInstanceId, taskId);

        // When & Then
        assertThatThrownBy(() -> taskService.update(taskId, "New Name", "New Desc", "new-form-key", 2, "{}"))
                .isInstanceOf(DuplicateFormKeyException.class)
                .hasMessageContaining("new-form-key");

        verify(validationService).validateTaskFormKeyUnique("new-form-key", processInstanceId, taskId);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldThrowExceptionWhenUpdatingTaskWithDuplicateOrder() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new InvalidOrderSequenceException(2, "Task", processInstanceId))
                .when(validationService).validateTaskOrderUnique(2, processInstanceId, taskId);

        // When & Then
        assertThatThrownBy(() -> taskService.update(taskId, "New Name", "New Desc", "form-key-1", 2, "{}"))
                .isInstanceOf(InvalidOrderSequenceException.class)
                .hasMessageContaining("order sequence");

        verify(validationService).validateTaskOrderUnique(2, processInstanceId, taskId);
        verify(taskRepository, never()).save(any());
    }
}
