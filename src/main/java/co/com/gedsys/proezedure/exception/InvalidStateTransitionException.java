package co.com.gedsys.proezedure.exception;

public class InvalidStateTransitionException extends RuntimeException {
    
    public InvalidStateTransitionException(String currentState, String targetState, String entityType) {
        super(String.format("Invalid state transition for %s: cannot change from %s to %s", 
              entityType, currentState, targetState));
    }
    
    public InvalidStateTransitionException(String message) {
        super(message);
    }
}
