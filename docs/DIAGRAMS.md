# Diagramas del Sistema

## 📋 Índice

- [Diagrama de Componentes](#diagrama-de-componentes)
- [<PERSON><PERSON><PERSON> de <PERSON>](#flujo-de-datos)
- [Estados del Sistema](#estados-del-sistema)
- [Arquitectura de Despliegue](#arquitectura-de-despliegue)
- [Diagramas de Secuencia](#diagramas-de-secuencia)

## 🏗️ Diagrama de Componentes

```mermaid
graph TB
    subgraph "🌐 API Layer"
        PDC[ProcessDefinitionController]
        PIC[ProcessInstanceController]
        TC[TaskController]
        SC[SubtaskController]
        VC[VariableController]
        UTC[UserTaskController]
        EH[GlobalExceptionHandler]
    end
    
    subgraph "💼 Service Layer"
        PDS[ProcessDefinitionService]
        PIS[ProcessInstanceService]
        TS[TaskService]
        SS[SubtaskService]
        VS[VariableService]
        VLS[ValidationService]
    end
    
    subgraph "🗃️ Repository Layer"
        PDR[ProcessDefinitionRepository]
        PIR[ProcessInstanceRepository]
        TR[TaskRepository]
        SR[SubtaskRepository]
    end
    
    subgraph "🛠️ Utilities"
        JU[JsonUtils]
        CONFIG[Configuration]
    end
    
    subgraph "🗄️ Database"
        PG[(PostgreSQL)]
    end
    
    PDC --> PDS
    PIC --> PIS
    TC --> TS
    SC --> SS
    VC --> VS
    UTC --> TS
    
    PDS --> VLS
    PIS --> VLS
    TS --> VLS
    TS --> VS
    SS --> VLS
    VS --> VLS
    
    PDS --> PDR
    PIS --> PIR
    TS --> TR
    SS --> SR
    VS --> PIS
    
    PDR --> PG
    PIR --> PG
    TR --> PG
    SR --> PG
    
    VS --> JU
    VLS --> JU
```

## 🔄 Flujo de Datos Completo

```mermaid
flowchart TD
    START([Inicio del Proceso])
    
    subgraph "📋 Definición"
        TPL[Crear Plantilla]
        VTPL[Validar Plantilla]
    end
    
    subgraph "🚀 Instanciación"
        INST[Crear Instancia]
        VINST[Validar Instancia]
        IVAR[Inicializar Variables]
    end
    
    subgraph "📝 Gestión de Tareas"
        CTASK[Crear Tarea]
        VTASK[Validar Tarea]
        ASSIGN[Asignar Tarea]
        
        subgraph "📋 Subtareas"
            CSUB[Crear Subtarea]
            VSUB[Validar Subtarea]
            COMPSUB[Completar Subtarea]
        end
        
        COMPTASK[Completar Tarea]
    end
    
    subgraph "🔄 Variables"
        PROP[Propagar Outputs]
        MERGE[Merge Variables]
        UPVAR[Actualizar Variables]
    end
    
    subgraph "✅ Finalización"
        COMPPROC[Completar Proceso]
        AUDIT[Auditoría]
    end
    
    START --> TPL
    TPL --> VTPL
    VTPL --> INST
    INST --> VINST
    VINST --> IVAR
    IVAR --> CTASK
    CTASK --> VTASK
    VTASK --> ASSIGN
    ASSIGN --> CSUB
    CSUB --> VSUB
    VSUB --> COMPSUB
    COMPSUB --> COMPTASK
    COMPTASK --> PROP
    PROP --> MERGE
    MERGE --> UPVAR
    UPVAR --> COMPPROC
    COMPPROC --> AUDIT
    
    COMPTASK -.-> CTASK
    AUDIT --> START
```

## 📊 Estados del Sistema Detallado

### Estados de ProcessDefinition

```mermaid
stateDiagram-v2
    [*] --> DRAFT : Crear borrador
    DRAFT --> ACTIVE : Activar
    ACTIVE --> INACTIVE : Desactivar
    INACTIVE --> ACTIVE : Reactivar
    ACTIVE --> DEPRECATED : Deprecar
    DEPRECATED --> [*] : Eliminar
    
    note right of ACTIVE : Puede crear instancias
    note right of INACTIVE : No puede crear instancias
    note right of DEPRECATED : Solo lectura
```

### Estados de ProcessInstance

```mermaid
stateDiagram-v2
    [*] --> RUNNING : Crear instancia
    RUNNING --> SUSPENDED : Suspender
    SUSPENDED --> RUNNING : Reanudar
    RUNNING --> COMPLETED : Completar todas las tareas
    RUNNING --> CANCELLED : Cancelar proceso
    SUSPENDED --> CANCELLED : Cancelar suspendido
    COMPLETED --> [*]
    CANCELLED --> [*]
    
    note right of RUNNING : Tareas activas
    note right of SUSPENDED : Tareas pausadas
    note right of COMPLETED : Proceso finalizado exitosamente
    note right of CANCELLED : Proceso terminado prematuramente
```

### Estados de Task

```mermaid
stateDiagram-v2
    [*] --> CREATED : Crear tarea
    CREATED --> ASSIGNED : Asignar usuario
    CREATED --> IN_PROGRESS : Iniciar sin asignar
    ASSIGNED --> IN_PROGRESS : Usuario inicia trabajo
    IN_PROGRESS --> COMPLETED : Completar con outputs
    
    CREATED --> CANCELLED : Cancelar
    ASSIGNED --> CANCELLED : Cancelar
    IN_PROGRESS --> CANCELLED : Cancelar
    
    COMPLETED --> [*]
    CANCELLED --> [*]
    
    note right of CREATED : Tarea disponible
    note right of ASSIGNED : Usuario específico asignado
    note right of IN_PROGRESS : Trabajo en curso
    note right of COMPLETED : Tarea finalizada con outputs
    note right of CANCELLED : Tarea cancelada
```

### Estados de Subtask

```mermaid
stateDiagram-v2
    [*] --> CREATED : Crear subtarea
    CREATED --> COMPLETED : Completar con outputs
    CREATED --> CANCELLED : Cancelar
    COMPLETED --> [*]
    CANCELLED --> [*]
    
    note right of CREATED : Subtarea pendiente
    note right of COMPLETED : Subtarea finalizada
    note right of CANCELLED : Subtarea cancelada
```

## 🏢 Arquitectura de Despliegue

```mermaid
graph TB
    subgraph "🌐 Internet"
        USER[👤 Usuario]
        API_CLIENT[📱 Cliente API]
    end
    
    subgraph "🔒 DMZ"
        LB[⚖️ Load Balancer]
        PROXY[🛡️ Reverse Proxy]
    end
    
    subgraph "🏢 Application Tier"
        APP1[🚀 Proezedure App 1]
        APP2[🚀 Proezedure App 2]
        APP3[🚀 Proezedure App 3]
    end
    
    subgraph "🗄️ Data Tier"
        DB_MASTER[(📊 PostgreSQL Master)]
        DB_REPLICA[(📊 PostgreSQL Replica)]
        REDIS[(🔄 Redis Cache)]
    end
    
    subgraph "📊 Monitoring"
        METRICS[📈 Metrics]
        LOGS[📝 Logs]
        ALERTS[🚨 Alerts]
    end
    
    USER --> LB
    API_CLIENT --> LB
    LB --> PROXY
    PROXY --> APP1
    PROXY --> APP2
    PROXY --> APP3
    
    APP1 --> DB_MASTER
    APP2 --> DB_MASTER
    APP3 --> DB_MASTER
    
    APP1 --> DB_REPLICA
    APP2 --> DB_REPLICA
    APP3 --> DB_REPLICA
    
    APP1 --> REDIS
    APP2 --> REDIS
    APP3 --> REDIS
    
    DB_MASTER --> DB_REPLICA
    
    APP1 --> METRICS
    APP2 --> METRICS
    APP3 --> METRICS
    
    APP1 --> LOGS
    APP2 --> LOGS
    APP3 --> LOGS
    
    METRICS --> ALERTS
    LOGS --> ALERTS
```

## 🔄 Diagramas de Secuencia

### Secuencia: Crear y Completar Proceso

```mermaid
sequenceDiagram
    participant U as Usuario
    participant API as API Gateway
    participant PS as ProcessService
    participant TS as TaskService
    participant VS as VariableService
    participant DB as Database
    
    Note over U,DB: Crear Proceso Completo
    
    U->>API: POST /process-instances
    API->>PS: create(request)
    PS->>DB: save(processInstance)
    DB-->>PS: processInstance
    PS-->>API: processInstance
    API-->>U: 201 Created
    
    Note over U,DB: Crear Tarea
    
    U->>API: POST /tasks
    API->>TS: create(request)
    TS->>DB: save(task)
    DB-->>TS: task
    TS-->>API: task
    API-->>U: 201 Created
    
    Note over U,DB: Completar Tarea
    
    U->>API: PATCH /tasks/{id}/complete
    API->>TS: complete(taskId, user, outputs)
    TS->>DB: update(task)
    TS->>VS: propagateOutputs(processId, outputs)
    VS->>DB: update(variables)
    DB-->>VS: updated
    VS-->>TS: success
    TS-->>API: completedTask
    API-->>U: 200 OK
```

### Secuencia: Validación de Negocio

```mermaid
sequenceDiagram
    participant C as Controller
    participant S as Service
    participant V as ValidationService
    participant R as Repository
    participant DB as Database
    
    Note over C,DB: Validación Completa
    
    C->>S: businessOperation(request)
    
    S->>V: validateReferentialIntegrity()
    V->>R: exists(entityId)
    R->>DB: SELECT EXISTS(...)
    DB-->>R: boolean
    R-->>V: exists
    alt Entity not exists
        V-->>S: ReferentialIntegrityException
        S-->>C: Exception
    end
    
    S->>V: validateBusinessRules()
    V->>R: checkConstraints()
    R->>DB: SELECT constraints
    DB-->>R: constraints
    R-->>V: valid
    alt Business rule violated
        V-->>S: BusinessRuleException
        S-->>C: Exception
    end
    
    S->>V: validateJsonStructure()
    V->>V: parseJson()
    alt Invalid JSON
        V-->>S: JsonValidationException
        S-->>C: Exception
    end
    
    Note over S: All validations passed
    S->>R: performOperation()
    R->>DB: INSERT/UPDATE
    DB-->>R: result
    R-->>S: entity
    S-->>C: success
```

## 📈 Diagrama de Flujo de Variables

```mermaid
flowchart LR
    subgraph "📋 Proceso"
        PI[ProcessInstance]
        VAR[Variables JSON]
    end
    
    subgraph "📝 Tarea 1"
        T1[Task 1]
        T1_IN[Inputs]
        T1_OUT[Outputs]
    end
    
    subgraph "📝 Tarea 2"
        T2[Task 2]
        T2_IN[Inputs]
        T2_OUT[Outputs]
    end
    
    subgraph "📝 Tarea N"
        TN[Task N]
        TN_IN[Inputs]
        TN_OUT[Outputs]
    end
    
    PI --> VAR
    VAR --> T1_IN
    T1 --> T1_OUT
    T1_OUT -.->|Propagación Automática| VAR
    
    VAR --> T2_IN
    T2 --> T2_OUT
    T2_OUT -.->|Propagación Automática| VAR
    
    VAR --> TN_IN
    TN --> TN_OUT
    TN_OUT -.->|Propagación Automática| VAR
    
    style VAR fill:#e1f5fe
    style T1_OUT fill:#c8e6c9
    style T2_OUT fill:#c8e6c9
    style TN_OUT fill:#c8e6c9
```

## 🔍 Diagrama de Validaciones

```mermaid
flowchart TD
    START([Operación de Negocio])
    
    subgraph "🔍 Validaciones de Entrada"
        V1[Validar Parámetros]
        V2[Validar JSON]
        V3[Validar Permisos]
    end
    
    subgraph "🔗 Validaciones de Integridad"
        V4[Validar Referencias]
        V5[Validar Unicidad]
        V6[Validar Secuencias]
    end
    
    subgraph "📋 Validaciones de Negocio"
        V7[Validar Estados]
        V8[Validar Transiciones]
        V9[Validar Reglas]
    end
    
    subgraph "💾 Operación"
        OP[Ejecutar Operación]
        SAVE[Guardar Cambios]
    end
    
    ERROR[❌ Error]
    SUCCESS[✅ Éxito]
    
    START --> V1
    V1 --> V2
    V2 --> V3
    V3 --> V4
    V4 --> V5
    V5 --> V6
    V6 --> V7
    V7 --> V8
    V8 --> V9
    V9 --> OP
    OP --> SAVE
    SAVE --> SUCCESS
    
    V1 -.-> ERROR
    V2 -.-> ERROR
    V3 -.-> ERROR
    V4 -.-> ERROR
    V5 -.-> ERROR
    V6 -.-> ERROR
    V7 -.-> ERROR
    V8 -.-> ERROR
    V9 -.-> ERROR
    OP -.-> ERROR
    SAVE -.-> ERROR
```

## 🎯 Diagrama de Casos de Uso

```mermaid
graph LR
    subgraph "👥 Actores"
        ADMIN[👨‍💼 Administrador]
        USER[👤 Usuario]
        SYSTEM[🤖 Sistema]
    end
    
    subgraph "📋 Gestión de Plantillas"
        UC1[Crear Plantilla]
        UC2[Modificar Plantilla]
        UC3[Activar/Desactivar]
    end
    
    subgraph "🚀 Gestión de Procesos"
        UC4[Crear Instancia]
        UC5[Consultar Estado]
        UC6[Completar Proceso]
    end
    
    subgraph "📝 Gestión de Tareas"
        UC7[Crear Tarea]
        UC8[Asignar Tarea]
        UC9[Completar Tarea]
        UC10[Consultar Tareas]
    end
    
    subgraph "🔄 Gestión de Variables"
        UC11[Actualizar Variables]
        UC12[Consultar Variables]
        UC13[Propagar Outputs]
    end
    
    ADMIN --> UC1
    ADMIN --> UC2
    ADMIN --> UC3
    
    USER --> UC4
    USER --> UC5
    USER --> UC7
    USER --> UC8
    USER --> UC9
    USER --> UC10
    USER --> UC11
    USER --> UC12
    
    SYSTEM --> UC6
    SYSTEM --> UC13
```
