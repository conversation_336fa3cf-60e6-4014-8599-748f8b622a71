package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotNull;

import java.util.UUID;

public class PropagateOutputsRequest {

    @NotNull(message = "Task ID is required")
    private UUID taskId;

    private String taskOutputs;

    private String[] outputFields;

    public PropagateOutputsRequest() {
    }

    public PropagateOutputsRequest(UUID taskId) {
        this.taskId = taskId;
    }

    public PropagateOutputsRequest(UUID taskId, String taskOutputs, String[] outputFields) {
        this.taskId = taskId;
        this.taskOutputs = taskOutputs;
        this.outputFields = outputFields;
    }

    public UUID getTaskId() {
        return taskId;
    }

    public void setTaskId(UUID taskId) {
        this.taskId = taskId;
    }

    public String getTaskOutputs() {
        return taskOutputs;
    }

    public void setTaskOutputs(String taskOutputs) {
        this.taskOutputs = taskOutputs;
    }

    public String[] getOutputFields() {
        return outputFields;
    }

    public void setOutputFields(String[] outputFields) {
        this.outputFields = outputFields;
    }

    public boolean hasCustomOutputs() {
        return taskOutputs != null && !taskOutputs.trim().isEmpty();
    }

    public boolean hasFieldMapping() {
        return outputFields != null && outputFields.length > 0;
    }
}
