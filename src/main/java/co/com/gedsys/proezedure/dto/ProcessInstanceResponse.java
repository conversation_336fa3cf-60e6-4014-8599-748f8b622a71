package co.com.gedsys.proezedure.dto;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.ProcessStatus;
import co.com.gedsys.proezedure.util.JsonConverter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.UUID;

public class ProcessInstanceResponse {

    private UUID id;
    private UUID templateId;
    private String name;
    private String description;
    private LinkedHashMap<String, Object> variables;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private ProcessStatus status;
    private LocalDate dueDate;

    public ProcessInstanceResponse() {
    }

    public ProcessInstanceResponse(ProcessInstance instance) {
        this.id = instance.getId();
        this.templateId = instance.getTemplateId();
        this.name = instance.getName();
        this.description = instance.getDescription();
        this.createdAt = instance.getCreatedAt();
        this.updatedAt = instance.getUpdatedAt();
        this.status = instance.getStatus();
        this.dueDate = instance.getDueDate();
        // variables se establecerá usando el método factory
    }

    public static ProcessInstanceResponse from(ProcessInstance instance, JsonConverter jsonConverter) {
        ProcessInstanceResponse response = new ProcessInstanceResponse();
        response.id = instance.getId();
        response.templateId = instance.getTemplateId();
        response.name = instance.getName();
        response.description = instance.getDescription();
        response.variables = jsonConverter.jsonToMapOrEmpty(instance.getVariables());
        response.createdAt = instance.getCreatedAt();
        response.updatedAt = instance.getUpdatedAt();
        response.status = instance.getStatus();
        response.dueDate = instance.getDueDate();
        return response;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getTemplateId() {
        return templateId;
    }

    public void setTemplateId(UUID templateId) {
        this.templateId = templateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LinkedHashMap<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(LinkedHashMap<String, Object> variables) {
        this.variables = variables;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public boolean isFromTemplate() {
        return templateId != null;
    }
}
