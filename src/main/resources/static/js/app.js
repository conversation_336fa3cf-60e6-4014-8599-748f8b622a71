/**
 * Aplicación principal - Router y gestión de estado
 */
window.App = {
  currentRoute: '',
  components: {},
  
  /**
   * Inicializa la aplicación
   */
  init() {
    console.log('Inicializando Proezedure Client...');
    
    // Registrar componentes
    this.registerComponents();
    
    // Inicializar router
    this.initRouter();
    
    // Inicializar sistema de notificaciones
    this.initNotifications();
    
    // Configurar event listeners globales
    this.setupGlobalEventListeners();
    
    // Cargar ruta inicial
    this.handleRouteChange();
    
    console.log('Proezedure Client inicializado correctamente');
  },

  /**
   * Registra todos los componentes disponibles
   */
  registerComponents() {
    this.components = {
      'dashboard': window.DashboardComponent,
      'process-definitions': window.ProcessDefinitionsComponent,
      'process-instances': window.ProcessInstancesComponent,
      'tasks': window.TasksComponent,
      'my-tasks': window.MyTasksComponent,
      'variables': window.VariablesComponent,
      'subtasks': window.SubtasksComponent
    };
  },

  /**
   * Inicializa el sistema de routing
   */
  initRouter() {
    // Escuchar cambios en el hash
    window.addEventListener('hashchange', () => {
      this.handleRouteChange();
    });
    
    // Manejar navegación con botones del navegador
    window.addEventListener('popstate', () => {
      this.handleRouteChange();
    });
  },

  /**
   * Maneja los cambios de ruta
   */
  handleRouteChange() {
    const hash = window.location.hash.slice(1) || '/';
    const [route, params] = this.parseRoute(hash);
    
    console.log('Navegando a:', route, params);
    
    this.currentRoute = route;
    this.loadComponent(route, params);
    this.updateActiveNavigation(route);
  },

  /**
   * Parsea la ruta y extrae parámetros
   */
  parseRoute(hash) {
    const [path, queryString] = hash.split('?');
    const route = path === '/' ? 'dashboard' : path.slice(1);
    
    const params = {};
    if (queryString) {
      const urlParams = new URLSearchParams(queryString);
      for (const [key, value] of urlParams) {
        params[key] = value;
      }
    }
    
    return [route, params];
  },

  /**
   * Carga y renderiza un componente
   */
  async loadComponent(route, params = {}) {
    try {
      // Mostrar loading
      DOMUtils.show('loading');
      
      // Buscar el componente
      const component = this.components[route];
      
      if (!component) {
        throw new Error(`Componente no encontrado: ${route}`);
      }
      
      // Renderizar el componente
      await component.render(params);
      
    } catch (error) {
      console.error('Error cargando componente:', error);
      this.showErrorPage(error.message);
    }
  },

  /**
   * Actualiza la navegación activa
   */
  updateActiveNavigation(route) {
    DOMUtils.updateActiveNav(route);
  },

  /**
   * Muestra una página de error
   */
  showErrorPage(message) {
    DOMUtils.hide('loading');
    
    const content = document.getElementById('app-content');
    if (content) {
      content.innerHTML = `
        <div class="card">
          <div class="card-body text-center">
            <h2>Error de Navegación</h2>
            <p class="text-gray-600 mb-4">${DOMUtils.escapeHtml(message)}</p>
            <button class="btn btn-primary" onclick="window.App.navigate('/')">
              Ir al Dashboard
            </button>
          </div>
        </div>
      `;
    }
  },

  /**
   * Navega a una ruta específica
   */
  navigate(route) {
    window.location.hash = route.startsWith('#') ? route : `#${route}`;
  },

  /**
   * Inicializa el sistema de notificaciones
   */
  initNotifications() {
    window.NotificationManager = {
      show(message, type = 'info', duration = 5000) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = DOMUtils.createElement('div', {
          className: `toast toast-${type}`
        }, `
          <div class="toast-content">
            <p>${DOMUtils.escapeHtml(message)}</p>
          </div>
          <button class="toast-close" onclick="this.parentElement.remove()">×</button>
        `);

        container.appendChild(toast);

        // Auto-remover después del tiempo especificado
        if (duration > 0) {
          setTimeout(() => {
            if (toast.parentElement) {
              toast.remove();
            }
          }, duration);
        }
      },

      success(message, duration) {
        this.show(message, 'success', duration);
      },

      error(message, duration) {
        this.show(message, 'error', duration);
      },

      warning(message, duration) {
        this.show(message, 'warning', duration);
      },

      info(message, duration) {
        this.show(message, 'info', duration);
      }
    };
  },

  /**
   * Configura event listeners globales
   */
  setupGlobalEventListeners() {
    // Manejar errores globales
    window.addEventListener('error', (event) => {
      console.error('Error global:', event.error);
      if (window.NotificationManager) {
        window.NotificationManager.error('Ha ocurrido un error inesperado');
      }
    });

    // Manejar promesas rechazadas
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Promesa rechazada:', event.reason);
      if (window.NotificationManager) {
        window.NotificationManager.error('Error en operación asíncrona');
      }
    });

    // Manejar clics en enlaces de navegación
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href^="#"]');
      if (link) {
        e.preventDefault();
        this.navigate(link.getAttribute('href'));
      }
    });

    // Configurar modal global
    this.setupModal();
  },

  /**
   * Configura el sistema de modal global
   */
  setupModal() {
    const modal = document.getElementById('modal');
    const modalOverlay = document.getElementById('modal-overlay');
    const modalClose = document.getElementById('modal-close');

    if (modal && modalOverlay && modalClose) {
      // Cerrar modal al hacer clic en overlay
      modalOverlay.addEventListener('click', () => {
        this.closeModal();
      });

      // Cerrar modal al hacer clic en botón cerrar
      modalClose.addEventListener('click', () => {
        this.closeModal();
      });

      // Cerrar modal con tecla Escape
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal.classList.contains('show')) {
          this.closeModal();
        }
      });
    }

    // Exponer funciones de modal globalmente
    window.ModalManager = {
      show: (title, content, footer = '') => {
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        const modalFooter = document.getElementById('modal-footer');

        if (modal && modalTitle && modalBody && modalFooter) {
          modalTitle.textContent = title;
          modalBody.innerHTML = content;
          modalFooter.innerHTML = footer;
          
          modal.classList.add('show');
          modal.setAttribute('aria-hidden', 'false');
          
          // Focus en el modal para accesibilidad
          modal.focus();
        }
      },

      hide: () => {
        App.closeModal();
      }
    };
  },

  /**
   * Cierra el modal
   */
  closeModal() {
    const modal = document.getElementById('modal');
    if (modal) {
      modal.classList.remove('show');
      modal.setAttribute('aria-hidden', 'true');
    }
  }
};

/**
 * Router simple para compatibilidad
 */
window.Router = {
  navigate: (route) => window.App.navigate(route)
};

/**
 * Inicializar la aplicación cuando el DOM esté listo
 */
document.addEventListener('DOMContentLoaded', () => {
  window.App.init();
});

/**
 * Fallback para navegadores que no soportan DOMContentLoaded
 */
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.App.init();
  });
} else {
  window.App.init();
}
