package co.com.gedsys.proezedure.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Configuración web para servir el cliente SPA y manejar rutas de frontend.
 * 
 * Esta configuración permite:
 * - Servir archivos estáticos desde /static
 * - Manejar rutas SPA con fallback a index.html
 * - Configurar recursos estáticos personalizados
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * Configura el manejo de recursos estáticos.
     * Spring Boot ya maneja /static por defecto, pero aquí podemos
     * agregar configuraciones adicionales si es necesario.
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Configuración adicional para recursos estáticos si es necesaria
        // Por defecto Spring Boot ya sirve desde /static, /public, /resources, /META-INF/resources
        
        // Ejemplo de configuración personalizada (comentado por ahora):
        // registry.addResourceHandler("/assets/**")
        //         .addResourceLocations("classpath:/static/assets/")
        //         .setCachePeriod(3600); // Cache por 1 hora
    }

    /**
     * Configura controladores de vista para manejar rutas SPA.
     *
     * Esto permite que todas las rutas que no coincidan con recursos estáticos
     * o endpoints de API sean redirigidas a index.html, permitiendo que el
     * router de JavaScript maneje la navegación del lado del cliente.
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // Redirige la raíz a index.html
        registry.addViewController("/")
                .setViewName("forward:/index.html");

        // Maneja rutas SPA simples (ej: /dashboard, /tasks)
        registry.addViewController("/{path:[^.]*}")
                .setViewName("forward:/index.html");
    }
}
