package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;
import co.com.gedsys.proezedure.dto.CreateTaskRequest;
import co.com.gedsys.proezedure.dto.TaskResponse;
import co.com.gedsys.proezedure.service.TaskService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/process-instances")
public class ProcessInstanceTaskController {

    private final TaskService taskService;

    public ProcessInstanceTaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @PostMapping("/{processInstanceId}/tasks")
    public ResponseEntity<TaskResponse> createTaskForProcess(
            @PathVariable UUID processInstanceId,
            @Valid @RequestBody CreateTaskRequest request) {
        
        // Override the processInstanceId from path
        request.setProcessInstanceId(processInstanceId);
        
        Task task = taskService.create(
            request.getProcessInstanceId(),
            request.getName(),
            request.getDescription(),
            request.getFormKey(),
            request.getOrder(),
            request.getInputs()
        );
        
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/{processInstanceId}/tasks")
    public ResponseEntity<List<TaskResponse>> findTasksByProcess(
            @PathVariable UUID processInstanceId,
            @RequestParam(required = false) TaskStatus status) {
        
        List<Task> tasks = taskService.findByProcessInstance(processInstanceId);
        
        if (status != null) {
            tasks = tasks.stream()
                    .filter(task -> task.getStatus() == status)
                    .collect(Collectors.toList());
        }
        
        List<TaskResponse> responses = tasks.stream()
                .map(TaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{processInstanceId}/tasks/next")
    public ResponseEntity<TaskResponse> findNextTask(@PathVariable UUID processInstanceId) {
        Task nextTask = taskService.findNextTaskInProcess(processInstanceId);
        
        if (nextTask == null) {
            return ResponseEntity.noContent().build();
        }
        
        TaskResponse response = new TaskResponse(nextTask);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{processInstanceId}/tasks/count")
    public ResponseEntity<Long> countTasksByStatus(
            @PathVariable UUID processInstanceId,
            @RequestParam TaskStatus status) {
        
        long count = taskService.countByProcessInstanceAndStatus(processInstanceId, status);
        return ResponseEntity.ok(count);
    }
}
