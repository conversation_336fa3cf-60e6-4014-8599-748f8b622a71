package co.com.gedsys.proezedure.dto;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class TaskResponse {

    private UUID id;
    private UUID processInstanceId;
    private String name;
    private String description;
    private String formKey;
    private Integer order;
    private String assignedTo;
    private TaskStatus status;
    private String inputs;
    private String outputs;
    private LocalDateTime createdAt;
    private LocalDateTime claimedAt;
    private LocalDateTime completedAt;
    private LocalDateTime updatedAt;
    private List<SubtaskResponse> subtasks;

    public TaskResponse() {
    }

    public TaskResponse(Task task) {
        this.id = task.getId();
        this.processInstanceId = task.getProcessInstanceId();
        this.name = task.getName();
        this.description = task.getDescription();
        this.formKey = task.getFormKey();
        this.order = task.getOrder();
        this.assignedTo = task.getAssignedTo();
        this.status = task.getStatus();
        this.inputs = task.getInputs();
        this.outputs = task.getOutputs();
        this.createdAt = task.getCreatedAt();
        this.claimedAt = task.getClaimedAt();
        this.completedAt = task.getCompletedAt();
        this.updatedAt = task.getUpdatedAt();
        this.subtasks = task.getSubtasks().stream()
                .map(SubtaskResponse::new)
                .collect(Collectors.toList());
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(UUID processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(String assignedTo) {
        this.assignedTo = assignedTo;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public String getInputs() {
        return inputs;
    }

    public void setInputs(String inputs) {
        this.inputs = inputs;
    }

    public String getOutputs() {
        return outputs;
    }

    public void setOutputs(String outputs) {
        this.outputs = outputs;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getClaimedAt() {
        return claimedAt;
    }

    public void setClaimedAt(LocalDateTime claimedAt) {
        this.claimedAt = claimedAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<SubtaskResponse> getSubtasks() {
        return subtasks;
    }

    public void setSubtasks(List<SubtaskResponse> subtasks) {
        this.subtasks = subtasks;
    }

    public boolean isAssigned() {
        return assignedTo != null;
    }

    public boolean isCompleted() {
        return status == TaskStatus.COMPLETED;
    }
}
