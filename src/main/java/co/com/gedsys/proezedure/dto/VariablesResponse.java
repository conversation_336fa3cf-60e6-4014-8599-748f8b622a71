package co.com.gedsys.proezedure.dto;

import co.com.gedsys.proezedure.util.JsonConverter;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.UUID;

public class VariablesResponse {

    private UUID processInstanceId;
    private LinkedHashMap<String, Object> variables;
    private LocalDateTime lastUpdated;

    public VariablesResponse() {
    }

    public VariablesResponse(UUID processInstanceId, String variables, LocalDateTime lastUpdated) {
        this.processInstanceId = processInstanceId;
        this.lastUpdated = lastUpdated;
        // variables se establecerá usando el método factory
    }

    public static VariablesResponse from(UUID processInstanceId, String variablesJson, LocalDateTime lastUpdated, JsonConverter jsonConverter) {
        VariablesResponse response = new VariablesResponse();
        response.processInstanceId = processInstanceId;
        response.variables = jsonConverter.jsonToMapOrEmpty(variablesJson);
        response.lastUpdated = lastUpdated;
        return response;
    }

    public UUID getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(UUID processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public LinkedHashMap<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(LinkedHashMap<String, Object> variables) {
        this.variables = variables;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
}
