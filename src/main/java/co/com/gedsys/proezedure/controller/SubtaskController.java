package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.Subtask;
import co.com.gedsys.proezedure.domain.SubtaskStatus;
import co.com.gedsys.proezedure.dto.CompleteSubtaskRequest;
import co.com.gedsys.proezedure.dto.CreateSubtaskRequest;
import co.com.gedsys.proezedure.dto.SubtaskResponse;
import co.com.gedsys.proezedure.service.SubtaskService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/subtasks")
public class SubtaskController {

    private final SubtaskService subtaskService;

    public SubtaskController(SubtaskService subtaskService) {
        this.subtaskService = subtaskService;
    }

    @PostMapping
    public ResponseEntity<SubtaskResponse> create(@Valid @RequestBody CreateSubtaskRequest request) {
        Subtask subtask = subtaskService.create(
            request.getTaskId(),
            request.getName(),
            request.getDescription(),
            request.getFormKey(),
            request.getOrder(),
            request.getInputs()
        );
        
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping
    public ResponseEntity<List<SubtaskResponse>> findAll(
            @RequestParam(required = false) SubtaskStatus status,
            @RequestParam(required = false) String formKey) {
        
        List<Subtask> subtasks;
        
        if (status != null) {
            subtasks = subtaskService.findByStatus(status);
        } else if (formKey != null) {
            subtasks = subtaskService.findByFormKey(formKey);
        } else {
            subtasks = subtaskService.findByStatus(SubtaskStatus.CREATED); // Default to available subtasks
        }
        
        List<SubtaskResponse> responses = subtasks.stream()
                .map(SubtaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{id}")
    public ResponseEntity<SubtaskResponse> findById(@PathVariable UUID id) {
        Subtask subtask = subtaskService.findById(id);
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/by-form-key")
    public ResponseEntity<SubtaskResponse> findByFormKeyAndTask(
            @RequestParam String formKey,
            @RequestParam UUID taskId) {
        
        Subtask subtask = subtaskService.findByFormKeyAndTask(formKey, taskId);
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<SubtaskResponse> update(
            @PathVariable UUID id,
            @Valid @RequestBody CreateSubtaskRequest request) {
        
        Subtask subtask = subtaskService.update(
            id,
            request.getName(),
            request.getDescription(),
            request.getFormKey(),
            request.getOrder(),
            request.getInputs()
        );
        
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/complete")
    public ResponseEntity<SubtaskResponse> complete(
            @PathVariable UUID id,
            @Valid @RequestBody CompleteSubtaskRequest request) {
        
        Subtask subtask = subtaskService.complete(id, request.getOutputs());
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<SubtaskResponse> updateStatus(
            @PathVariable UUID id,
            @RequestParam SubtaskStatus status) {
        
        Subtask subtask = subtaskService.updateStatus(id, status);
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/inputs")
    public ResponseEntity<SubtaskResponse> updateInputs(
            @PathVariable UUID id,
            @RequestBody String inputs) {
        
        Subtask subtask = subtaskService.updateInputs(id, inputs);
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/outputs")
    public ResponseEntity<SubtaskResponse> updateOutputs(
            @PathVariable UUID id,
            @RequestBody String outputs) {
        
        Subtask subtask = subtaskService.updateOutputs(id, outputs);
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable UUID id) {
        subtaskService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
