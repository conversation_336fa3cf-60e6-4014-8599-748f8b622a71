package co.com.gedsys.proezedure.repository;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.ProcessStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface ProcessInstanceRepository extends JpaRepository<ProcessInstance, UUID> {

    List<ProcessInstance> findByStatus(ProcessStatus status);

    List<ProcessInstance> findByTemplateId(UUID templateId);

    List<ProcessInstance> findByTemplateIdAndStatus(UUID templateId, ProcessStatus status);

    List<ProcessInstance> findByNameContainingIgnoreCase(String name);

    @Query("SELECT pi FROM ProcessInstance pi WHERE pi.status = :status ORDER BY pi.createdAt DESC")
    List<ProcessInstance> findByStatusOrderByCreatedAt(@Param("status") ProcessStatus status);

    @Query("SELECT pi FROM ProcessInstance pi WHERE pi.createdAt BETWEEN :startDate AND :endDate")
    List<ProcessInstance> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                                @Param("endDate") LocalDateTime endDate);

    @Query("SELECT COUNT(pi) FROM ProcessInstance pi WHERE pi.status = :status")
    long countByStatus(@Param("status") ProcessStatus status);

    @Query("SELECT COUNT(pi) FROM ProcessInstance pi WHERE pi.templateId = :templateId")
    long countByTemplateId(@Param("templateId") UUID templateId);

    @Query("SELECT pi FROM ProcessInstance pi WHERE pi.templateId IS NULL")
    List<ProcessInstance> findAdHocInstances();
}
