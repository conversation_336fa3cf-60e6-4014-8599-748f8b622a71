# Arquitectura del Sistema

## 📋 Índice

- [Visión General](#visión-general)
- [Arquitectura de Capas](#arquitectura-de-capas)
- [Modelo de Dominio](#modelo-de-dominio)
- [Flujo de Datos](#flujo-de-datos)
- [Patrones de Diseño](#patrones-de-diseño)
- [Tecnologías Utilizadas](#tecnologías-utilizadas)

## 🏗️ Visión General

Proezedure implementa una **arquitectura de capas** basada en **Domain-Driven Design (DDD)** y **Clean Architecture**, utilizando Spring Boot como framework principal. El sistema está diseñado para ser escalable, mantenible y testeable.

### Principios Arquitectónicos

- **Separación de Responsabilidades**: Cada capa tiene una responsabilidad específica
- **Inversión de Dependencias**: Las capas superiores no dependen de las inferiores
- **Single Responsibility Principle**: Cada clase tiene una única razón para cambiar
- **Open/Closed Principle**: Abierto para extensión, cerrado para modificación

## 🏛️ Arquitectura de Capas

```mermaid
graph TB
    subgraph "🌐 Presentation Layer"
        C[Controllers]
        EH[Exception Handlers]
        DTO[DTOs]
    end
    
    subgraph "💼 Business Layer"
        S[Services]
        VS[Validation Service]
        VRS[Variable Service]
    end
    
    subgraph "🗃️ Data Access Layer"
        R[Repositories]
        E[Entities]
    end
    
    subgraph "🛠️ Infrastructure Layer"
        DB[(PostgreSQL)]
        JSON[JSON Utils]
        CONFIG[Configuration]
    end
    
    C --> S
    C --> DTO
    EH --> C
    
    S --> VS
    S --> VRS
    S --> R
    
    R --> E
    E --> DB
    
    S --> JSON
    VS --> JSON
```

### Capa de Presentación (Presentation Layer)

**Responsabilidad**: Manejo de requests HTTP, validación de entrada, serialización/deserialización

**Componentes**:
- **Controllers**: Endpoints REST que exponen la funcionalidad
- **DTOs**: Objetos de transferencia de datos para requests/responses
- **Exception Handlers**: Manejo centralizado de excepciones

**Tecnologías**: Spring Web, Bean Validation, Jackson

### Capa de Negocio (Business Layer)

**Responsabilidad**: Lógica de negocio, validaciones, orquestación de operaciones

**Componentes**:
- **Services**: Implementan la lógica de negocio principal
- **Validation Service**: Validaciones centralizadas de reglas de negocio
- **Variable Service**: Gestión especializada de variables JSON

**Patrones**: Service Layer, Strategy Pattern, Command Pattern

### Capa de Acceso a Datos (Data Access Layer)

**Responsabilidad**: Persistencia y recuperación de datos

**Componentes**:
- **Repositories**: Interfaces de acceso a datos usando Spring Data JPA
- **Entities**: Representación de las entidades del dominio

**Tecnologías**: Spring Data JPA, Hibernate, PostgreSQL

### Capa de Infraestructura (Infrastructure Layer)

**Responsabilidad**: Servicios técnicos transversales

**Componentes**:
- **Database**: PostgreSQL con soporte JSON nativo
- **JSON Utils**: Utilidades para manipulación de JSON
- **Configuration**: Configuración de Spring Boot

## 🎯 Modelo de Dominio

### Entidades Principales

```mermaid
classDiagram
    class ProcessDefinition {
        +UUID id
        +String name
        +String description
        +String version
        +String defaultVariables
        +ProcessDefinitionStatus status
        +LocalDateTime createdAt
        +create()
        +activate()
        +deactivate()
    }
    
    class ProcessInstance {
        +UUID id
        +UUID templateId
        +String name
        +String description
        +String variables
        +ProcessStatus status
        +LocalDate dueDate
        +LocalDateTime createdAt
        +LocalDateTime updatedAt
        +updateVariables()
        +complete()
        +cancel()
    }
    
    class Task {
        +UUID id
        +UUID processInstanceId
        +String name
        +String formKey
        +Integer order
        +String assignedTo
        +String inputs
        +String outputs
        +TaskStatus status
        +assign()
        +claim()
        +complete()
        +canBeCompleted()
    }
    
    class Subtask {
        +UUID id
        +UUID taskId
        +String name
        +String formKey
        +Integer order
        +String inputs
        +String outputs
        +SubtaskStatus status
        +complete()
    }
    
    ProcessDefinition ||--o{ ProcessInstance : template
    ProcessInstance ||--o{ Task : contains
    Task ||--o{ Subtask : contains
```

### Estados del Sistema

```mermaid
stateDiagram-v2
    [*] --> ACTIVE : ProcessDefinition
    ACTIVE --> INACTIVE
    INACTIVE --> ACTIVE
    
    [*] --> RUNNING : ProcessInstance
    RUNNING --> COMPLETED
    RUNNING --> CANCELLED
    
    [*] --> CREATED : Task
    CREATED --> ASSIGNED
    CREATED --> IN_PROGRESS
    ASSIGNED --> IN_PROGRESS
    IN_PROGRESS --> COMPLETED
    CREATED --> CANCELLED
    ASSIGNED --> CANCELLED
    IN_PROGRESS --> CANCELLED
    
    [*] --> CREATED : Subtask
    CREATED --> COMPLETED
    CREATED --> CANCELLED
```

## 🔄 Flujo de Datos

### Flujo de Creación de Proceso

```mermaid
sequenceDiagram
    participant C as Client
    participant PC as ProcessController
    participant PS as ProcessService
    participant VS as ValidationService
    participant PR as ProcessRepository
    participant DB as Database
    
    C->>PC: POST /api/process-instances
    PC->>PS: create(request)
    PS->>VS: validateProcessInstanceExists()
    VS-->>PS: validation ok
    PS->>PR: save(processInstance)
    PR->>DB: INSERT
    DB-->>PR: saved entity
    PR-->>PS: processInstance
    PS-->>PC: processInstance
    PC-->>C: 201 Created
```

### Flujo de Completar Tarea

```mermaid
sequenceDiagram
    participant C as Client
    participant TC as TaskController
    participant TS as TaskService
    participant VS as ValidationService
    participant VRS as VariableService
    participant TR as TaskRepository
    participant DB as Database
    
    C->>TC: PATCH /api/tasks/{id}/complete
    TC->>TS: complete(taskId, username, outputs)
    TS->>VS: validateUserPermission()
    TS->>VS: validateTaskStateTransition()
    TS->>VS: validateAllSubtasksCompleted()
    TS->>VS: validateJsonStructure()
    VS-->>TS: all validations ok
    TS->>TR: save(task)
    TR->>DB: UPDATE
    TS->>VRS: propagateTaskOutputs()
    VRS->>DB: UPDATE variables
    TS-->>TC: completed task
    TC-->>C: 200 OK
```

## 🎨 Patrones de Diseño

### Repository Pattern
- **Propósito**: Abstrae el acceso a datos
- **Implementación**: Spring Data JPA repositories
- **Beneficio**: Desacopla la lógica de negocio de la persistencia

### Service Layer Pattern
- **Propósito**: Encapsula la lógica de negocio
- **Implementación**: Clases de servicio con `@Service`
- **Beneficio**: Centraliza las operaciones de negocio

### DTO Pattern
- **Propósito**: Transferencia de datos entre capas
- **Implementación**: Clases POJO con validaciones
- **Beneficio**: Controla qué datos se exponen en la API

### Strategy Pattern
- **Propósito**: Validaciones dinámicas
- **Implementación**: ValidationService con métodos especializados
- **Beneficio**: Flexibilidad en las validaciones

### Command Pattern
- **Propósito**: Encapsula operaciones como objetos
- **Implementación**: DTOs de request como comandos
- **Beneficio**: Facilita testing y logging

## 🛠️ Tecnologías Utilizadas

### Backend Framework
- **Spring Boot 3.5.3**: Framework principal
- **Spring Web**: API REST
- **Spring Data JPA**: Acceso a datos
- **Spring Validation**: Validaciones

### Base de Datos
- **PostgreSQL 15+**: Base de datos principal
- **Hibernate**: ORM
- **JSON Support**: Tipos JSON nativos

### Testing
- **JUnit 5**: Framework de testing
- **Mockito**: Mocking
- **AssertJ**: Assertions fluidas
- **Spring Boot Test**: Testing de integración

### Utilidades
- **Jackson**: Serialización JSON
- **Maven**: Gestión de dependencias
- **H2**: Base de datos en memoria para tests

## 📊 Métricas de Calidad

### Cobertura de Tests
- **96 tests unitarios** ejecutándose
- **Cobertura de servicios**: 100%
- **Cobertura de validaciones**: 100%
- **Cobertura de utilidades**: 100%

### Principios SOLID
- ✅ **Single Responsibility**: Cada clase tiene una responsabilidad
- ✅ **Open/Closed**: Extensible sin modificar código existente
- ✅ **Liskov Substitution**: Interfaces bien definidas
- ✅ **Interface Segregation**: Interfaces específicas
- ✅ **Dependency Inversion**: Dependencias inyectadas

### Clean Code
- ✅ **Nombres descriptivos**: Variables y métodos claros
- ✅ **Funciones pequeñas**: Máximo 20 líneas por método
- ✅ **Sin duplicación**: DRY principle aplicado
- ✅ **Comentarios mínimos**: Código auto-documentado

## 🔮 Consideraciones Futuras

### Escalabilidad
- **Microservicios**: Dividir en servicios independientes
- **Cache**: Redis para variables frecuentemente accedidas
- **Message Queue**: RabbitMQ para procesamiento asíncrono

### Seguridad
- **JWT Authentication**: Autenticación basada en tokens
- **Role-Based Access**: Control de acceso por roles
- **Audit Trail**: Registro de todas las operaciones

### Monitoreo
- **Actuator**: Métricas de Spring Boot
- **Micrometer**: Métricas personalizadas
- **Logging**: Structured logging con Logback
