package co.com.gedsys.proezedure.exception;

import java.util.UUID;

public class ReferentialIntegrityException extends RuntimeException {
    
    public ReferentialIntegrityException(String entityType, UUID entityId, String referencedType, UUID referencedId) {
        super(String.format("Referential integrity violation: %s %s references non-existent %s %s", 
              entityType, entityId, referencedType, referencedId));
    }
    
    public ReferentialIntegrityException(String message) {
        super(message);
    }
}
