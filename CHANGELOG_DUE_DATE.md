# Changelog - Fecha de Vencimiento para Procesos + Integración Flyway

## Resumen de Cambios

Se ha agregado la funcionalidad de fecha de vencimiento (`dueDate`) a los procesos del sistema y se ha integrado Flyway como sistema de migración de base de datos. Esta fecha representa el día, mes y año límite para completar un proceso.

## Archivos Modificados

### Entidades de Dominio

1. **ProcessInstance.java**
   - Agregado campo `dueDate` de tipo `LocalDate`
   - Agregados constructores que incluyen `dueDate`
   - Agregados getters y setters para `dueDate`

### DTOs

2. **ProcessInstanceResponse.java**
   - Agregado campo `dueDate` de tipo `LocalDate`
   - Actualizado constructor y método `from()` para incluir `dueDate`
   - Agregados getters y setters para `dueDate`

3. **CreateProcessInstanceRequest.java**
   - Agregado campo `dueDate` de tipo `LocalDate`
   - Agregados constructores que incluyen `dueDate`
   - Agregados getters y setters para `dueDate`

### Servicios

4. **ProcessInstanceService.java**
   - Agregados métodos sobrecargados para crear procesos con `dueDate`:
     - `createFromTemplate()` con parámetro `dueDate`
     - `createAdHoc()` con parámetro `dueDate`
     - `update()` con parámetro `dueDate`

### Controladores

5. **ProcessInstanceController.java**
   - Actualizado método `create()` para manejar `dueDate` opcional
   - Actualizado método `update()` para manejar `dueDate` opcional
   - Actualizado método `createInstanceFromTemplate()` para manejar `dueDate` opcional

### Base de Datos y Migraciones

6. **pom.xml**
   - Agregadas dependencias de Flyway (`flyway-core` y `flyway-database-postgresql`)

7. **Configuración de Flyway**
   - `application.properties`: Configuración base de Flyway
   - `application-dev.properties`: Configuración para desarrollo
   - `application-prod.properties`: Configuración para producción
   - `application-test.properties`: Flyway deshabilitado para tests

8. **Migraciones de Flyway**
   - `V1__Initial_schema.sql`: Migración inicial con esquema completo
   - `V2__Add_due_date_to_process_instances.sql`: Migración para agregar fecha de vencimiento

### Pruebas

7. **ProcessInstanceRepositoryTest.java**
   - Agregado test `shouldSaveAndRetrieveProcessInstanceWithDueDate()` para verificar funcionalidad

### Documentación

9. **README.md**
   - Actualizado diagrama ER para incluir campo `dueDate`
   - Agregados ejemplos de uso con fecha de vencimiento
   - Agregada sección de migraciones de Flyway

10. **docs/ARCHITECTURE.md**
    - Actualizado diagrama de clases para incluir campo `dueDate`

11. **docs/FLYWAY.md**
    - Nueva documentación completa sobre Flyway
    - Configuración por entornos
    - Mejores prácticas y troubleshooting

## Características Implementadas

### Fecha de Vencimiento
- ✅ Campo `dueDate` opcional en procesos
- ✅ Soporte para crear procesos con fecha de vencimiento
- ✅ Soporte para actualizar fecha de vencimiento de procesos existentes
- ✅ Compatibilidad hacia atrás (procesos sin fecha de vencimiento siguen funcionando)
- ✅ Validación automática de tipos de datos
- ✅ Pruebas unitarias para verificar funcionalidad

### Integración Flyway
- ✅ Sistema de migraciones automáticas con Flyway
- ✅ Configuración por entornos (dev, prod, test)
- ✅ Migraciones versionadas y validadas
- ✅ Compatibilidad con PostgreSQL 15.13
- ✅ Historial de migraciones en base de datos
- ✅ Documentación completa de Flyway

### Documentación
- ✅ Documentación actualizada con ejemplos
- ✅ Guía completa de Flyway
- ✅ Mejores prácticas y troubleshooting

## Uso

### Crear Proceso con Fecha de Vencimiento

```json
{
  "name": "Proceso de Aprobación",
  "description": "Proceso que debe completarse antes del fin de año",
  "dueDate": "2025-12-31",
  "variables": {
    "clienteId": "12345",
    "montoSolicitud": 50000
  }
}
```

### Crear Proceso sin Fecha de Vencimiento

```json
{
  "name": "Proceso sin Fecha Límite",
  "description": "Proceso que no tiene fecha de vencimiento",
  "variables": {
    "clienteId": "67890"
  }
}
```

## Migración de Base de Datos

### Con Flyway (Recomendado)

Las migraciones se ejecutan automáticamente al iniciar la aplicación:

```bash
# Desarrollo
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev

# Ver estado de migraciones
./mvnw flyway:info
```

### Migración Manual (Solo si es necesario)

Para aplicar los cambios manualmente en una base de datos existente:

```sql
-- Migración V1: Esquema inicial (si la BD está vacía)
-- Ver: src/main/resources/db/migration/V1__Initial_schema.sql

-- Migración V2: Agregar fecha de vencimiento
ALTER TABLE process_instances
ADD COLUMN due_date DATE;

CREATE INDEX idx_process_instances_due_date ON process_instances(due_date);
CREATE INDEX idx_process_instances_overdue ON process_instances(due_date, status)
WHERE due_date IS NOT NULL AND status = 'RUNNING';

COMMENT ON COLUMN process_instances.due_date IS 'Fecha de vencimiento del proceso (día, mes, año)';
```

## Notas Técnicas

### Fecha de Vencimiento
- El campo `dueDate` es opcional (puede ser `null`)
- Se usa `LocalDate` para representar solo fecha (sin hora)
- La columna en base de datos es de tipo `DATE`
- Los procesos existentes mantendrán `dueDate` como `null`
- La funcionalidad es completamente compatible hacia atrás

### Flyway
- Versión utilizada: 11.7.2 (incluida en Spring Boot 3.5.3)
- Dependencia adicional: `flyway-database-postgresql` para soporte PostgreSQL 15.13
- Configuración diferenciada por entornos
- Tabla de historial: `flyway_schema_history`
- Migraciones en: `src/main/resources/db/migration/`

### Configuración de Entornos
- **Desarrollo**: Flyway habilitado, `clean` permitido, `baseline-on-migrate=true`
- **Producción**: Flyway habilitado, `clean` deshabilitado, validación estricta
- **Testing**: Flyway deshabilitado, Hibernate `create-drop` para pruebas rápidas
