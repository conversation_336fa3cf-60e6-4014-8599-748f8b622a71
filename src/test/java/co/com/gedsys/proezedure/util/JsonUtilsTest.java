package co.com.gedsys.proezedure.util;

import co.com.gedsys.proezedure.exception.InvalidJsonException;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class JsonUtilsTest {

    @Test
    void shouldValidateValidJson() {
        // Given
        String validJson = "{\"key\": \"value\", \"number\": 123}";

        // When
        boolean result = JsonUtils.isValidJson(validJson);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void shouldInvalidateInvalidJson() {
        // Given
        String invalidJson = "{invalid json}";

        // When
        boolean result = JsonUtils.isValidJson(invalidJson);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldInvalidateNullOrEmptyJson() {
        // When & Then
        assertThat(JsonUtils.isValidJson(null)).isFalse();
        assertThat(JsonUtils.isValidJson("")).isFalse();
        assertThat(JsonUtils.isValidJson("   ")).isFalse();
    }

    @Test
    void shouldThrowExceptionForInvalidJsonValidation() {
        // Given
        String invalidJson = "{invalid json}";

        // When & Then
        assertThatThrownBy(() -> JsonUtils.validateJson(invalidJson))
                .isInstanceOf(InvalidJsonException.class)
                .hasMessageContaining("Malformed JSON string");
    }

    @Test
    void shouldNormalizeJson() {
        // Given
        String unnormalizedJson = "{ \"key\" : \"value\" , \"number\" : 123 }";
        String expectedNormalized = "{\"key\":\"value\",\"number\":123}";

        // When
        String result = JsonUtils.normalizeJson(unnormalizedJson);

        // Then
        assertThat(result).isEqualTo(expectedNormalized);
    }

    @Test
    void shouldReturnEmptyObjectForNullJson() {
        // When
        String result = JsonUtils.normalizeJson(null);

        // Then
        assertThat(result).isEqualTo("{}");
    }

    @Test
    void shouldMergeJsonObjects() {
        // Given
        String baseJson = "{\"clienteId\": \"12345\", \"datos\": {\"nombre\": \"Juan\"}}";
        String updateJson = "{\"datos\": {\"apellido\": \"Perez\"}, \"estado\": \"activo\"}";
        String expectedMerged = "{\"clienteId\":\"12345\",\"datos\":{\"nombre\":\"Juan\",\"apellido\":\"Perez\"},\"estado\":\"activo\"}";

        // When
        String result = JsonUtils.mergeJson(baseJson, updateJson);

        // Then
        assertThat(result).isEqualTo(expectedMerged);
    }

    @Test
    void shouldPatchJsonObjects() {
        // Given
        String baseJson = "{\"clienteId\": \"12345\", \"estado\": \"inicial\"}";
        String patchJson = "{\"estado\": \"activo\", \"monto\": 50000}";
        String expectedPatched = "{\"clienteId\":\"12345\",\"estado\":\"activo\",\"monto\":50000}";

        // When
        String result = JsonUtils.patchJson(baseJson, patchJson);

        // Then
        assertThat(result).isEqualTo(expectedPatched);
    }

    @Test
    void shouldExtractSpecificFields() {
        // Given
        String sourceJson = "{\"clienteId\": \"12345\", \"estado\": \"activo\", \"monto\": 50000, \"observaciones\": \"Todo bien\"}";
        String expectedExtracted = "{\"clienteId\":\"12345\",\"monto\":50000}";

        // When
        String result = JsonUtils.extractFields(sourceJson, "clienteId", "monto");

        // Then
        assertThat(result).isEqualTo(expectedExtracted);
    }

    @Test
    void shouldExtractNonExistentFields() {
        // Given
        String sourceJson = "{\"clienteId\": \"12345\", \"estado\": \"activo\"}";
        String expectedExtracted = "{}";

        // When
        String result = JsonUtils.extractFields(sourceJson, "nonexistent", "alsoNonexistent");

        // Then
        assertThat(result).isEqualTo(expectedExtracted);
    }

    @Test
    void shouldHandleEmptyJsonInMerge() {
        // Given
        String baseJson = "{\"key\": \"value\"}";
        String emptyJson = "";

        // When
        String result = JsonUtils.mergeJson(baseJson, emptyJson);

        // Then
        assertThat(result).isEqualTo(baseJson);
    }

    @Test
    void shouldHandleNullJsonInPatch() {
        // Given
        String baseJson = "{\"key\": \"value\"}";

        // When
        String result = JsonUtils.patchJson(baseJson, null);

        // Then
        assertThat(result).isEqualTo(baseJson);
    }

    @Test
    void shouldThrowExceptionForInvalidJsonInMerge() {
        // Given
        String validJson = "{\"key\": \"value\"}";
        String invalidJson = "{invalid}";

        // When & Then
        assertThatThrownBy(() -> JsonUtils.mergeJson(validJson, invalidJson))
                .isInstanceOf(InvalidJsonException.class)
                .hasMessageContaining("Failed to merge JSON");
    }

    @Test
    void shouldThrowExceptionForInvalidJsonInPatch() {
        // Given
        String validJson = "{\"key\": \"value\"}";
        String invalidJson = "{invalid}";

        // When & Then
        assertThatThrownBy(() -> JsonUtils.patchJson(validJson, invalidJson))
                .isInstanceOf(InvalidJsonException.class)
                .hasMessageContaining("Failed to patch JSON");
    }

    @Test
    void shouldThrowExceptionForInvalidJsonInExtract() {
        // Given
        String invalidJson = "{invalid}";

        // When & Then
        assertThatThrownBy(() -> JsonUtils.extractFields(invalidJson, "field"))
                .isInstanceOf(InvalidJsonException.class)
                .hasMessageContaining("Failed to extract fields");
    }
}
