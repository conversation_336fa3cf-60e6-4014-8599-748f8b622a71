package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.*;
import co.com.gedsys.proezedure.exception.*;
import co.com.gedsys.proezedure.repository.ProcessInstanceRepository;
import co.com.gedsys.proezedure.repository.SubtaskRepository;
import co.com.gedsys.proezedure.repository.TaskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ValidationServiceTest {

    @Mock
    private ProcessInstanceRepository processInstanceRepository;

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private SubtaskRepository subtaskRepository;

    @InjectMocks
    private ValidationService validationService;

    private UUID processInstanceId;
    private UUID taskId;
    private UUID subtaskId;
    private Task testTask;
    private Subtask testSubtask;

    @BeforeEach
    void setUp() {
        processInstanceId = UUID.randomUUID();
        taskId = UUID.randomUUID();
        subtaskId = UUID.randomUUID();
        
        testTask = new Task(processInstanceId, "Test Task", "Description", "form-key-1", 1, "{}");
        testTask.setId(taskId);
        testTask.setStatus(TaskStatus.CREATED);
        
        testSubtask = new Subtask(taskId, "Test Subtask", "Description", "subtask-form-key-1", 1, "{}");
        testSubtask.setId(subtaskId);
        testSubtask.setStatus(SubtaskStatus.CREATED);
    }

    @Test
    void shouldValidateProcessInstanceExists() {
        // Given
        when(processInstanceRepository.existsById(processInstanceId)).thenReturn(true);

        // When & Then
        assertDoesNotThrow(() -> validationService.validateProcessInstanceExists(processInstanceId));
        verify(processInstanceRepository).existsById(processInstanceId);
    }

    @Test
    void shouldThrowExceptionWhenProcessInstanceNotExists() {
        // Given
        when(processInstanceRepository.existsById(processInstanceId)).thenReturn(false);

        // When & Then
        assertThatThrownBy(() -> validationService.validateProcessInstanceExists(processInstanceId))
                .isInstanceOf(ReferentialIntegrityException.class)
                .hasMessageContaining("ProcessInstance");

        verify(processInstanceRepository).existsById(processInstanceId);
    }

    @Test
    void shouldValidateTaskExists() {
        // Given
        when(taskRepository.existsById(taskId)).thenReturn(true);

        // When & Then
        assertDoesNotThrow(() -> validationService.validateTaskExists(taskId));
        verify(taskRepository).existsById(taskId);
    }

    @Test
    void shouldThrowExceptionWhenTaskNotExists() {
        // Given
        when(taskRepository.existsById(taskId)).thenReturn(false);

        // When & Then
        assertThatThrownBy(() -> validationService.validateTaskExists(taskId))
                .isInstanceOf(ReferentialIntegrityException.class)
                .hasMessageContaining("Task");

        verify(taskRepository).existsById(taskId);
    }

    @Test
    void shouldValidateTaskBelongsToProcess() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // When & Then
        assertDoesNotThrow(() -> validationService.validateTaskBelongsToProcess(taskId, processInstanceId));
        verify(taskRepository).findById(taskId);
    }

    @Test
    void shouldThrowExceptionWhenTaskNotBelongsToProcess() {
        // Given
        UUID wrongProcessId = UUID.randomUUID();
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // When & Then
        assertThatThrownBy(() -> validationService.validateTaskBelongsToProcess(taskId, wrongProcessId))
                .isInstanceOf(ReferentialIntegrityException.class)
                .hasMessageContaining("does not belong to ProcessInstance");

        verify(taskRepository).findById(taskId);
    }

    @Test
    void shouldValidateSubtaskBelongsToTask() {
        // Given
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));

        // When & Then
        assertDoesNotThrow(() -> validationService.validateSubtaskBelongsToTask(subtaskId, taskId));
        verify(subtaskRepository).findById(subtaskId);
    }

    @Test
    void shouldThrowExceptionWhenSubtaskNotBelongsToTask() {
        // Given
        UUID wrongTaskId = UUID.randomUUID();
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));

        // When & Then
        assertThatThrownBy(() -> validationService.validateSubtaskBelongsToTask(subtaskId, wrongTaskId))
                .isInstanceOf(ReferentialIntegrityException.class)
                .hasMessageContaining("does not belong to Task");

        verify(subtaskRepository).findById(subtaskId);
    }

    @Test
    void shouldValidateTaskFormKeyUnique() {
        // Given
        when(taskRepository.existsByFormKeyAndProcessInstanceId("unique-form-key", processInstanceId)).thenReturn(false);

        // When & Then
        assertDoesNotThrow(() -> validationService.validateTaskFormKeyUnique("unique-form-key", processInstanceId, null));
        verify(taskRepository).existsByFormKeyAndProcessInstanceId("unique-form-key", processInstanceId);
    }

    @Test
    void shouldThrowExceptionWhenTaskFormKeyNotUnique() {
        // Given
        when(taskRepository.existsByFormKeyAndProcessInstanceId("duplicate-form-key", processInstanceId)).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> validationService.validateTaskFormKeyUnique("duplicate-form-key", processInstanceId, null))
                .isInstanceOf(DuplicateFormKeyException.class)
                .hasMessageContaining("duplicate-form-key");

        verify(taskRepository).existsByFormKeyAndProcessInstanceId("duplicate-form-key", processInstanceId);
    }

    @Test
    void shouldValidateTaskStateTransition() {
        // When & Then - Valid transitions
        assertDoesNotThrow(() -> validationService.validateTaskStateTransition(TaskStatus.CREATED, TaskStatus.ASSIGNED));
        assertDoesNotThrow(() -> validationService.validateTaskStateTransition(TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS));
        assertDoesNotThrow(() -> validationService.validateTaskStateTransition(TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED));
    }

    @Test
    void shouldThrowExceptionForInvalidTaskStateTransition() {
        // When & Then - Invalid transitions
        assertThatThrownBy(() -> validationService.validateTaskStateTransition(TaskStatus.COMPLETED, TaskStatus.IN_PROGRESS))
                .isInstanceOf(InvalidStateTransitionException.class)
                .hasMessageContaining("COMPLETED")
                .hasMessageContaining("IN_PROGRESS");

        assertThatThrownBy(() -> validationService.validateTaskStateTransition(TaskStatus.CANCELLED, TaskStatus.ASSIGNED))
                .isInstanceOf(InvalidStateTransitionException.class)
                .hasMessageContaining("CANCELLED")
                .hasMessageContaining("ASSIGNED");
    }

    @Test
    void shouldValidateSubtaskStateTransition() {
        // When & Then - Valid transitions
        assertDoesNotThrow(() -> validationService.validateSubtaskStateTransition(SubtaskStatus.CREATED, SubtaskStatus.COMPLETED));
        assertDoesNotThrow(() -> validationService.validateSubtaskStateTransition(SubtaskStatus.CREATED, SubtaskStatus.CANCELLED));
    }

    @Test
    void shouldThrowExceptionForInvalidSubtaskStateTransition() {
        // When & Then - Invalid transitions
        assertThatThrownBy(() -> validationService.validateSubtaskStateTransition(SubtaskStatus.COMPLETED, SubtaskStatus.CREATED))
                .isInstanceOf(InvalidStateTransitionException.class)
                .hasMessageContaining("COMPLETED")
                .hasMessageContaining("CREATED");
    }

    @Test
    void shouldValidateJsonStructure() {
        // When & Then - Valid JSON
        assertDoesNotThrow(() -> validationService.validateJsonStructure("{\"key\": \"value\"}", "testField"));
        assertDoesNotThrow(() -> validationService.validateJsonStructure(null, "testField"));
        assertDoesNotThrow(() -> validationService.validateJsonStructure("", "testField"));
    }

    @Test
    void shouldThrowExceptionForInvalidJsonStructure() {
        // When & Then - Invalid JSON
        assertThatThrownBy(() -> validationService.validateJsonStructure("{invalid json}", "testField"))
                .isInstanceOf(JsonSchemaValidationException.class)
                .hasMessageContaining("testField");
    }

    @Test
    void shouldValidateUserPermission() {
        // Given
        testTask.setAssignedTo("jmarin");

        // When & Then - Valid permission
        assertDoesNotThrow(() -> validationService.validateUserPermission(testTask, "jmarin"));
    }

    @Test
    void shouldThrowExceptionForInvalidUserPermission() {
        // Given
        testTask.setAssignedTo("jmarin");

        // When & Then - Invalid permission
        assertThatThrownBy(() -> validationService.validateUserPermission(testTask, "otheruser"))
                .isInstanceOf(TaskNotAssignedException.class)
                .hasMessageContaining("otheruser");
    }

    @Test
    void shouldValidateAllSubtasksCompleted() {
        // Given
        when(subtaskRepository.areAllSubtasksCompleted(taskId)).thenReturn(true);

        // When & Then
        assertDoesNotThrow(() -> validationService.validateAllSubtasksCompleted(taskId));
        verify(subtaskRepository).areAllSubtasksCompleted(taskId);
    }

    @Test
    void shouldThrowExceptionWhenNotAllSubtasksCompleted() {
        // Given
        when(subtaskRepository.areAllSubtasksCompleted(taskId)).thenReturn(false);

        // When & Then
        assertThatThrownBy(() -> validationService.validateAllSubtasksCompleted(taskId))
                .isInstanceOf(TaskCannotBeCompletedException.class)
                .hasMessageContaining(taskId.toString());

        verify(subtaskRepository).areAllSubtasksCompleted(taskId);
    }
}
