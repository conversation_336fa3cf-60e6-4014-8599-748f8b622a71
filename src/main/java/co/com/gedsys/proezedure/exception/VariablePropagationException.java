package co.com.gedsys.proezedure.exception;

import java.util.UUID;

public class VariablePropagationException extends RuntimeException {
    
    public VariablePropagationException(UUID processInstanceId, String message) {
        super("Failed to propagate variables for process instance " + processInstanceId + ": " + message);
    }
    
    public VariablePropagationException(UUID processInstanceId, String message, Throwable cause) {
        super("Failed to propagate variables for process instance " + processInstanceId + ": " + message, cause);
    }
}
