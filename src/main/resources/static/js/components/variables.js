/**
 * Componente Variables - Gestión de variables de proceso
 */
window.VariablesComponent = {
  name: 'variables',
  currentProcessId: null,
  currentVariables: {},
  
  async render(params = {}) {
    try {
      DOMUtils.show('loading');
      
      // Obtener ID del proceso desde parámetros
      this.currentProcessId = params.process || new URLSearchParams(window.location.search).get('process');
      
      if (!this.currentProcessId) {
        throw new Error('ID de proceso requerido');
      }
      
      // Obtener variables del proceso
      const variablesData = await API.variables.getByProcess(this.currentProcessId);
      this.currentVariables = variablesData.variables || {};
      
      // Obtener información del proceso
      const processInfo = await API.processInstances.getById(this.currentProcessId);
      
      DOMUtils.hide('loading');
      
      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Inicio', href: '#/' },
        { text: 'Procesos', href: '#/process-instances' },
        { text: processInfo.name, href: `#/process-instances/${this.currentProcessId}` },
        { text: 'Variables' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate(processInfo);

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando variables');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate(processInfo) {
    const variableEntries = Object.entries(this.currentVariables);
    
    return `
      <div class="variables">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Variables del Proceso</h1>
              <p class="text-gray-600">Gestiona las variables de: <strong>${DOMUtils.escapeHtml(processInfo.name)}</strong></p>
            </div>
            <div class="flex gap-2">
              <button class="btn btn-secondary" data-action="add-variable">
                <span>➕</span>
                Nueva Variable
              </button>
              <button class="btn btn-outline" data-action="edit-all">
                <span>✏️</span>
                Editar Todo
              </button>
              <button class="btn btn-outline" data-action="clear-all" style="color: var(--color-gray-700);">
                <span>🗑️</span>
                Limpiar Todo
              </button>
            </div>
          </div>
        </div>

        <!-- Información del proceso -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-3">
              <div>
                <strong>Estado:</strong> ${Formatters.formatStatus(processInfo.status)}
              </div>
              <div>
                <strong>Creado:</strong> ${Formatters.formatDateTime(processInfo.createdAt)}
              </div>
              <div>
                <strong>Total Variables:</strong> ${variableEntries.length}
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de variables -->
        <div class="variables-list" id="variables-list">
          ${this.getVariablesListTemplate(variableEntries)}
        </div>
      </div>

      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .gap-2 {
          gap: var(--spacing-2);
        }
        
        .variable-item {
          background: var(--color-white);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius-lg);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-3);
          transition: var(--transition);
        }
        
        .variable-item:hover {
          box-shadow: var(--shadow-sm);
        }
        
        .variable-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-2);
        }
        
        .variable-name {
          font-weight: 600;
          color: var(--color-gray-800);
          font-size: var(--font-size-lg);
        }
        
        .variable-type {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
          background: var(--color-gray-100);
          padding: var(--spacing-1) var(--spacing-2);
          border-radius: var(--border-radius);
        }
        
        .variable-value {
          background: var(--color-gray-50);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius);
          padding: var(--spacing-3);
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: var(--font-size-sm);
          white-space: pre-wrap;
          word-break: break-all;
          max-height: 200px;
          overflow-y: auto;
        }
        
        .variable-actions {
          display: flex;
          gap: var(--spacing-2);
          margin-top: var(--spacing-3);
        }
      </style>
    `;
  },

  getVariablesListTemplate(variableEntries) {
    if (variableEntries.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <h3>No hay variables</h3>
            <p class="text-gray-600 mb-4">Este proceso no tiene variables definidas.</p>
            <button class="btn btn-primary" data-action="add-variable">
              <span>➕</span>
              Agregar Primera Variable
            </button>
          </div>
        </div>
      `;
    }

    return variableEntries.map(([name, value]) => {
      const valueType = this.getValueType(value);
      const formattedValue = this.formatValue(value);
      
      return `
        <div class="variable-item" data-name="${DOMUtils.escapeHtml(name)}">
          <div class="variable-header">
            <div>
              <div class="variable-name">${DOMUtils.escapeHtml(name)}</div>
              <div class="variable-type">${valueType}</div>
            </div>
            <div class="variable-actions">
              <button class="btn btn-sm btn-outline" data-action="edit-variable" data-name="${DOMUtils.escapeHtml(name)}">
                Editar
              </button>
              <button class="btn btn-sm btn-outline" data-action="delete-variable" data-name="${DOMUtils.escapeHtml(name)}" style="color: var(--color-gray-700);">
                Eliminar
              </button>
            </div>
          </div>
          
          <div class="variable-value">${DOMUtils.escapeHtml(formattedValue)}</div>
        </div>
      `;
    }).join('');
  },

  getValueType(value) {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'string') return 'string';
    return 'unknown';
  },

  formatValue(value) {
    if (value === null) return 'null';
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar variables</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener las variables del proceso.</p>
          <button class="btn btn-primary" onclick="window.VariablesComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  setupEventListeners() {
    // Acciones de botones
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const name = actionButton.dataset.name;

      this.handleAction(action, name);
    });
  },

  async handleAction(action, name) {
    try {
      switch (action) {
        case 'add-variable':
          this.showAddVariableModal();
          break;
        case 'edit-variable':
          this.showEditVariableModal(name);
          break;
        case 'delete-variable':
          await this.deleteVariable(name);
          break;
        case 'edit-all':
          this.showEditAllModal();
          break;
        case 'clear-all':
          await this.clearAllVariables();
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  showAddVariableModal() {
    const modalContent = `
      <form id="add-variable-form">
        <div class="form-group">
          <label class="form-label" for="variable-name">Nombre de Variable *</label>
          <input type="text" class="form-input" id="variable-name" name="name" required 
                 placeholder="nombreVariable">
        </div>
        
        <div class="form-group">
          <label class="form-label" for="variable-value">Valor (JSON) *</label>
          <textarea class="form-textarea" id="variable-value" name="value" required 
                    placeholder='"texto" o 123 o true o {"objeto": "valor"}' rows="4"></textarea>
          <small class="form-help">Ingresa el valor en formato JSON válido.</small>
        </div>
      </form>
    `;
    
    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="save-variable-btn">Agregar Variable</button>
    `;
    
    ModalManager.show('Nueva Variable', modalContent, modalFooter);
    
    // Event listener para guardar
    document.getElementById('save-variable-btn').addEventListener('click', async () => {
      await this.handleAddVariable();
    });
  },

  async handleAddVariable() {
    const form = document.getElementById('add-variable-form');
    if (!DOMUtils.validateForm(form)) return;

    try {
      const formData = new FormData(form);
      const name = formData.get('name');
      const valueStr = formData.get('value');
      
      // Parsear el valor JSON
      let value;
      try {
        value = JSON.parse(valueStr);
      } catch (e) {
        throw new Error('El valor debe ser JSON válido');
      }

      await API.variables.setSpecificVariable(this.currentProcessId, name, value);
      
      ModalManager.hide();
      window.NotificationManager.success('Variable agregada exitosamente');
      await this.render({ process: this.currentProcessId });
      
    } catch (error) {
      API.utils.handleError(error, 'agregando variable');
    }
  },

  showEditVariableModal(name) {
    const currentValue = this.currentVariables[name];
    const formattedValue = typeof currentValue === 'object' ?
      JSON.stringify(currentValue, null, 2) :
      JSON.stringify(currentValue);

    const modalContent = `
      <form id="edit-variable-form">
        <div class="form-group">
          <label class="form-label" for="edit-variable-name">Nombre de Variable *</label>
          <input type="text" class="form-input" id="edit-variable-name" name="name" required
                 value="${DOMUtils.escapeHtml(name)}" readonly>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-variable-value">Valor (JSON) *</label>
          <textarea class="form-textarea" id="edit-variable-value" name="value" required
                    rows="6">${DOMUtils.escapeHtml(formattedValue)}</textarea>
          <small class="form-help">Modifica el valor en formato JSON válido.</small>
        </div>
      </form>
    `;

    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="update-variable-btn">Actualizar Variable</button>
    `;

    ModalManager.show('Editar Variable', modalContent, modalFooter);

    // Event listener para actualizar
    document.getElementById('update-variable-btn').addEventListener('click', async () => {
      await this.handleEditVariable(name);
    });
  },

  async handleEditVariable(originalName) {
    const form = document.getElementById('edit-variable-form');
    if (!DOMUtils.validateForm(form)) return;

    try {
      const formData = new FormData(form);
      const valueStr = formData.get('value');

      // Parsear el valor JSON
      let value;
      try {
        value = JSON.parse(valueStr);
      } catch (e) {
        throw new Error('El valor debe ser JSON válido');
      }

      await API.variables.setSpecificVariable(this.currentProcessId, originalName, value);

      ModalManager.hide();
      window.NotificationManager.success('Variable actualizada exitosamente');
      await this.render({ process: this.currentProcessId });

    } catch (error) {
      API.utils.handleError(error, 'actualizando variable');
    }
  },

  async deleteVariable(name) {
    if (confirm(`¿Estás seguro de que quieres eliminar la variable "${name}"?`)) {
      try {
        // Crear una copia de las variables sin la variable a eliminar
        const updatedVariables = { ...this.currentVariables };
        delete updatedVariables[name];

        await API.variables.updateByProcess(this.currentProcessId, updatedVariables);

        window.NotificationManager.success('Variable eliminada exitosamente');
        await this.render({ process: this.currentProcessId });

      } catch (error) {
        API.utils.handleError(error, 'eliminando variable');
      }
    }
  },

  showEditAllModal() {
    const formattedVariables = JSON.stringify(this.currentVariables, null, 2);

    const modalContent = `
      <form id="edit-all-variables-form">
        <div class="form-group">
          <label class="form-label" for="all-variables">Todas las Variables (JSON) *</label>
          <textarea class="form-textarea" id="all-variables" name="variables" required
                    rows="12" style="font-family: monospace;">${DOMUtils.escapeHtml(formattedVariables)}</textarea>
          <small class="form-help">Edita todas las variables como un objeto JSON. Ten cuidado con la sintaxis.</small>
        </div>
      </form>
    `;

    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="update-all-variables-btn">Actualizar Todas</button>
    `;

    ModalManager.show('Editar Todas las Variables', modalContent, modalFooter);

    // Event listener para actualizar todas
    document.getElementById('update-all-variables-btn').addEventListener('click', async () => {
      await this.handleEditAllVariables();
    });
  },

  async handleEditAllVariables() {
    const form = document.getElementById('edit-all-variables-form');
    if (!DOMUtils.validateForm(form)) return;

    try {
      const formData = new FormData(form);
      const variablesStr = formData.get('variables');

      // Parsear las variables JSON
      let variables;
      try {
        variables = JSON.parse(variablesStr);
      } catch (e) {
        throw new Error('El JSON de variables no es válido');
      }

      if (typeof variables !== 'object' || Array.isArray(variables)) {
        throw new Error('Las variables deben ser un objeto JSON');
      }

      await API.variables.updateByProcess(this.currentProcessId, variables);

      ModalManager.hide();
      window.NotificationManager.success('Variables actualizadas exitosamente');
      await this.render({ process: this.currentProcessId });

    } catch (error) {
      API.utils.handleError(error, 'actualizando variables');
    }
  },

  async clearAllVariables() {
    if (confirm('¿Estás seguro de que quieres eliminar TODAS las variables? Esta acción no se puede deshacer.')) {
      try {
        await API.variables.clearByProcess(this.currentProcessId);

        window.NotificationManager.success('Variables eliminadas exitosamente');
        await this.render({ process: this.currentProcessId });

      } catch (error) {
        API.utils.handleError(error, 'eliminando variables');
      }
    }
  }
};
