# Guía para Desarrolladores

## 📋 Índice

- [Configuración del Entorno](#configuración-del-entorno)
- [Estructura del Proyecto](#estructura-del-proyecto)
- [Estándar<PERSON> de Código](#estándares-de-código)
- [Testing](#testing)
- [Contribución](#contribución)
- [Debugging](#debugging)

## 🛠️ Configuración del Entorno

### Prerrequisitos

- **Java 21+** (OpenJDK recomendado)
- **Maven 3.8+**
- **PostgreSQL 15+**
- **IDE**: IntelliJ IDEA o VS Code con extensiones Java
- **Git**

### Configuración Local

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd proezedure
```

2. **Configurar PostgreSQL**
```sql
-- Crear base de datos
CREATE DATABASE proezedure_dev;
CREATE USER proezedure_dev WITH PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE proezedure_dev TO proezedure_dev;

-- Para testing
CREATE DATABASE proezedure_test;
GRANT ALL PRIVILEGES ON DATABASE proezedure_test TO proezedure_dev;
```

3. **Variables de entorno**
```bash
# Desarrollo
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=proezedure_dev
export DB_USERNAME=proezedure_dev
export DB_PASSWORD=dev_password

# Testing
export TEST_DB_NAME=proezedure_test
```

4. **Ejecutar la aplicación**
```bash
./mvnw spring-boot:run
```

### Perfiles de Spring

- **default**: Desarrollo local
- **test**: Testing automatizado
- **prod**: Producción (futuro)

## 📁 Estructura del Proyecto

```
src/
├── main/
│   ├── java/co/com/gedsys/proezedure/
│   │   ├── controller/          # Controladores REST
│   │   │   ├── ProcessDefinitionController.java
│   │   │   ├── ProcessInstanceController.java
│   │   │   ├── TaskController.java
│   │   │   ├── VariableController.java
│   │   │   └── ...
│   │   ├── domain/              # Entidades del dominio
│   │   │   ├── ProcessDefinition.java
│   │   │   ├── ProcessInstance.java
│   │   │   ├── Task.java
│   │   │   ├── Subtask.java
│   │   │   └── enums/
│   │   ├── dto/                 # Data Transfer Objects
│   │   │   ├── request/
│   │   │   └── response/
│   │   ├── exception/           # Excepciones personalizadas
│   │   │   ├── GlobalExceptionHandler.java
│   │   │   └── ...
│   │   ├── repository/          # Repositorios JPA
│   │   │   ├── ProcessDefinitionRepository.java
│   │   │   └── ...
│   │   ├── service/             # Servicios de negocio
│   │   │   ├── ProcessDefinitionService.java
│   │   │   ├── ValidationService.java
│   │   │   └── ...
│   │   └── util/                # Utilidades
│   │       └── JsonUtils.java
│   └── resources/
│       ├── application.yml      # Configuración principal
│       └── application-test.yml # Configuración de testing
└── test/
    └── java/co/com/gedsys/proezedure/
        ├── repository/          # Tests de repositorios
        ├── service/             # Tests de servicios
        └── util/                # Tests de utilidades
```

## 📝 Estándares de Código

### Convenciones de Nomenclatura

#### Clases
```java
// Entidades: Sustantivos en singular
public class ProcessDefinition { }
public class Task { }

// Servicios: Sustantivo + Service
public class TaskService { }
public class ValidationService { }

// Controladores: Sustantivo + Controller
public class TaskController { }

// DTOs: Propósito + Request/Response
public class CreateTaskRequest { }
public class TaskResponse { }

// Excepciones: Descripción + Exception
public class TaskNotFoundException extends RuntimeException { }
```

#### Métodos
```java
// CRUD operations
public Task create(CreateTaskRequest request) { }
public Task findById(UUID id) { }
public Task update(UUID id, UpdateTaskRequest request) { }
public void delete(UUID id) { }

// Business operations
public Task assign(UUID taskId, String username) { }
public Task complete(UUID taskId, String username, String outputs) { }

// Validations
public void validateTaskExists(UUID taskId) { }
public boolean canBeCompleted(Task task) { }

// Queries
public List<Task> findByStatus(TaskStatus status) { }
public boolean existsByFormKey(String formKey) { }
```

#### Variables
```java
// Descriptivas y en camelCase
private final TaskRepository taskRepository;
private final ValidationService validationService;

// Constantes en UPPER_SNAKE_CASE
private static final String DEFAULT_STATUS = "CREATED";
private static final int MAX_RETRY_ATTEMPTS = 3;
```

### Principios de Clean Code

#### 1. Funciones Pequeñas
```java
// ✅ Bueno: Una responsabilidad
public Task assign(UUID taskId, String username) {
    Task task = findById(taskId);
    task.setAssignedTo(username);
    task.setStatus(TaskStatus.ASSIGNED);
    return taskRepository.save(task);
}

// ❌ Malo: Múltiples responsabilidades
public Task assignAndValidateAndNotify(UUID taskId, String username) {
    // Demasiadas responsabilidades en una función
}
```

#### 2. Sin Duplicación (DRY)
```java
// ✅ Bueno: Lógica centralizada
public void validateJsonStructure(String json, String fieldName) {
    if (json != null && !JsonUtils.isValidJson(json)) {
        throw new JsonSchemaValidationException(fieldName, "valid JSON", json);
    }
}

// Usar en múltiples lugares
validationService.validateJsonStructure(inputs, "inputs");
validationService.validateJsonStructure(outputs, "outputs");
```

#### 3. Nombres Descriptivos
```java
// ✅ Bueno
public boolean areAllSubtasksCompleted(UUID taskId) {
    return subtaskRepository.findByTaskId(taskId)
        .stream()
        .allMatch(subtask -> subtask.getStatus() == SubtaskStatus.COMPLETED);
}

// ❌ Malo
public boolean check(UUID id) {
    // ¿Qué se está verificando?
}
```

### Manejo de Excepciones

#### Excepciones Específicas
```java
// ✅ Crear excepciones específicas
public class TaskNotFoundException extends RuntimeException {
    public TaskNotFoundException(UUID taskId) {
        super("Task not found with id: " + taskId);
    }
}

// ✅ Usar en servicios
public Task findById(UUID taskId) {
    return taskRepository.findById(taskId)
        .orElseThrow(() -> new TaskNotFoundException(taskId));
}
```

#### Validaciones
```java
// ✅ Validaciones al inicio del método
public Task complete(UUID taskId, String username, String outputs) {
    Task task = findById(taskId);
    
    // Todas las validaciones primero
    validationService.validateUserPermission(task, username);
    validationService.validateTaskStateTransition(task.getStatus(), TaskStatus.COMPLETED);
    validationService.validateAllSubtasksCompleted(taskId);
    validationService.validateJsonStructure(outputs, "outputs");
    
    // Lógica de negocio después
    task.complete(outputs);
    return taskRepository.save(task);
}
```

## 🧪 Testing

### Estructura de Tests

#### Tests Unitarios
```java
@ExtendWith(MockitoExtension.class)
class TaskServiceTest {
    
    @Mock
    private TaskRepository taskRepository;
    
    @Mock
    private ValidationService validationService;
    
    @InjectMocks
    private TaskService taskService;
    
    @Test
    void shouldCreateTask() {
        // Given
        CreateTaskRequest request = new CreateTaskRequest(/* ... */);
        Task expectedTask = new Task(/* ... */);
        when(taskRepository.save(any(Task.class))).thenReturn(expectedTask);
        
        // When
        Task result = taskService.create(request);
        
        // Then
        assertThat(result).isEqualTo(expectedTask);
        verify(taskRepository).save(any(Task.class));
    }
}
```

#### Tests de Repositorio
```java
@DataJpaTest
class TaskRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private TaskRepository taskRepository;
    
    @Test
    void shouldFindTasksByStatus() {
        // Given
        Task task = new Task(/* ... */);
        task.setStatus(TaskStatus.IN_PROGRESS);
        entityManager.persistAndFlush(task);
        
        // When
        List<Task> result = taskRepository.findByStatus(TaskStatus.IN_PROGRESS);
        
        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getStatus()).isEqualTo(TaskStatus.IN_PROGRESS);
    }
}
```

### Convenciones de Testing

#### Nomenclatura
```java
// Patrón: should[ExpectedBehavior]When[StateUnderTest]
@Test
void shouldThrowExceptionWhenTaskNotFound() { }

@Test
void shouldReturnTaskWhenValidIdProvided() { }

@Test
void shouldUpdateTaskStatusWhenValidTransition() { }
```

#### Estructura Given-When-Then
```java
@Test
void shouldCompleteTaskWhenAllValidationsPass() {
    // Given - Configurar el estado inicial
    UUID taskId = UUID.randomUUID();
    Task task = createTestTask();
    when(taskRepository.findById(taskId)).thenReturn(Optional.of(task));
    
    // When - Ejecutar la acción
    Task result = taskService.complete(taskId, "jmarin", "{}");
    
    // Then - Verificar el resultado
    assertThat(result.getStatus()).isEqualTo(TaskStatus.COMPLETED);
    verify(validationService).validateUserPermission(task, "jmarin");
}
```

### Ejecutar Tests

```bash
# Todos los tests
./mvnw test

# Tests específicos
./mvnw test -Dtest=TaskServiceTest

# Tests con cobertura
./mvnw test jacoco:report

# Tests en modo watch
./mvnw test -Dspring.devtools.restart.enabled=false
```

## 🤝 Contribución

### Flujo de Trabajo

1. **Fork** el repositorio
2. **Crear rama** para la feature
```bash
git checkout -b feature/nueva-funcionalidad
```

3. **Desarrollar** siguiendo los estándares
4. **Escribir tests** para la nueva funcionalidad
5. **Ejecutar tests** y verificar que pasen
```bash
./mvnw test
```

6. **Commit** con mensaje descriptivo
```bash
git commit -m "feat: agregar validación de JSON en variables"
```

7. **Push** y crear **Pull Request**

### Convenciones de Commit

Usar [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Nuevas funcionalidades
git commit -m "feat: agregar endpoint para completar tareas"

# Corrección de bugs
git commit -m "fix: corregir validación de estado de tarea"

# Documentación
git commit -m "docs: actualizar documentación de API"

# Tests
git commit -m "test: agregar tests para ValidationService"

# Refactoring
git commit -m "refactor: extraer lógica de validación a servicio separado"
```

### Code Review

#### Checklist para Reviewers

- [ ] ¿El código sigue los estándares establecidos?
- [ ] ¿Hay tests para la nueva funcionalidad?
- [ ] ¿Los nombres son descriptivos?
- [ ] ¿Se manejan correctamente las excepciones?
- [ ] ¿La documentación está actualizada?
- [ ] ¿No hay duplicación de código?

## 🐛 Debugging

### Logging

```java
// Usar SLF4J
private static final Logger logger = LoggerFactory.getLogger(TaskService.class);

public Task complete(UUID taskId, String username, String outputs) {
    logger.debug("Completing task {} for user {}", taskId, username);
    
    try {
        // Lógica de negocio
        Task result = /* ... */;
        logger.info("Task {} completed successfully", taskId);
        return result;
    } catch (Exception e) {
        logger.error("Failed to complete task {}: {}", taskId, e.getMessage(), e);
        throw e;
    }
}
```

### Configuración de Logs

```yaml
# application-dev.yml
logging:
  level:
    co.com.gedsys.proezedure: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
```

### Debugging en IDE

1. **Breakpoints**: Colocar en líneas críticas
2. **Conditional Breakpoints**: Para casos específicos
3. **Watch Variables**: Monitorear estado de objetos
4. **Step Through**: Ejecutar línea por línea

### Herramientas Útiles

- **H2 Console**: `/h2-console` para inspeccionar BD en tests
- **Actuator**: `/actuator/health` para verificar estado
- **Spring Boot DevTools**: Hot reload durante desarrollo
