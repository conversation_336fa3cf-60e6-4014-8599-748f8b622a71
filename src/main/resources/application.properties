spring.application.name=proezedure

# Docker Compose Configuration (disabled by default)
spring.docker.compose.enabled=false

# Database Configuration (Development - H2 in-memory)
spring.datasource.url=jdbc:h2:mem:proezedure
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console (for development)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Database Configuration (Production - should be configured via environment variables)
# spring.datasource.url=${DATABASE_URL:*******************************************}
# spring.datasource.username=${DATABASE_USERNAME:proezedure_user}
# spring.datasource.password=${DATABASE_PASSWORD:proezedure_pass}
# spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Flyway Configuration (disabled for development with H2)
spring.flyway.enabled=false
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true

# Logging
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.root=INFO
