package co.com.gedsys.proezedure.repository;

import co.com.gedsys.proezedure.domain.Subtask;
import co.com.gedsys.proezedure.domain.SubtaskStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface SubtaskRepository extends JpaRepository<Subtask, UUID> {

    List<Subtask> findByTaskId(UUID taskId);

    List<Subtask> findByTaskIdOrderByOrder(UUID taskId);

    List<Subtask> findByStatus(SubtaskStatus status);

    List<Subtask> findByTaskIdAndStatus(UUID taskId, SubtaskStatus status);

    List<Subtask> findByFormKey(String formKey);

    Optional<Subtask> findByFormKeyAndTaskId(String formKey, UUID taskId);

    @Query("SELECT s FROM Subtask s WHERE s.taskId = :taskId AND s.status = :status ORDER BY s.order")
    List<Subtask> findByTaskIdAndStatusOrderByOrder(@Param("taskId") UUID taskId, 
                                                    @Param("status") SubtaskStatus status);

    @Query("SELECT COUNT(s) FROM Subtask s WHERE s.taskId = :taskId AND s.status = :status")
    long countByTaskIdAndStatus(@Param("taskId") UUID taskId, 
                               @Param("status") SubtaskStatus status);

    @Query("SELECT COUNT(s) FROM Subtask s WHERE s.taskId = :taskId")
    long countByTaskId(@Param("taskId") UUID taskId);

    @Query("SELECT s FROM Subtask s WHERE s.taskId = :taskId AND s.order = " +
           "(SELECT MIN(s2.order) FROM Subtask s2 WHERE s2.taskId = :taskId AND s2.status != 'COMPLETED')")
    Optional<Subtask> findNextSubtaskInTask(@Param("taskId") UUID taskId);

    @Query("SELECT CASE WHEN COUNT(s) = COUNT(CASE WHEN s.status = 'COMPLETED' THEN 1 END) THEN true ELSE false END " +
           "FROM Subtask s WHERE s.taskId = :taskId")
    boolean areAllSubtasksCompleted(@Param("taskId") UUID taskId);

    boolean existsByFormKeyAndTaskId(String formKey, UUID taskId);
}
