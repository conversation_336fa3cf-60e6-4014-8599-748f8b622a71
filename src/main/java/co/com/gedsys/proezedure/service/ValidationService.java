package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.*;
import co.com.gedsys.proezedure.exception.*;
import co.com.gedsys.proezedure.repository.ProcessInstanceRepository;
import co.com.gedsys.proezedure.repository.SubtaskRepository;
import co.com.gedsys.proezedure.repository.TaskRepository;
import co.com.gedsys.proezedure.util.JsonUtils;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class ValidationService {

    private final ProcessInstanceRepository processInstanceRepository;
    private final TaskRepository taskRepository;
    private final SubtaskRepository subtaskRepository;

    public ValidationService(ProcessInstanceRepository processInstanceRepository,
                           TaskRepository taskRepository,
                           SubtaskRepository subtaskRepository) {
        this.processInstanceRepository = processInstanceRepository;
        this.taskRepository = taskRepository;
        this.subtaskRepository = subtaskRepository;
    }

    public void validateProcessInstanceExists(UUID processInstanceId) {
        if (!processInstanceRepository.existsById(processInstanceId)) {
            throw new ReferentialIntegrityException("Task", null, "ProcessInstance", processInstanceId);
        }
    }

    public void validateTaskExists(UUID taskId) {
        if (!taskRepository.existsById(taskId)) {
            throw new ReferentialIntegrityException("Subtask", null, "Task", taskId);
        }
    }

    public void validateTaskBelongsToProcess(UUID taskId, UUID processInstanceId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));
        
        if (!task.getProcessInstanceId().equals(processInstanceId)) {
            throw new ReferentialIntegrityException(
                String.format("Task %s does not belong to ProcessInstance %s", taskId, processInstanceId)
            );
        }
    }

    public void validateSubtaskBelongsToTask(UUID subtaskId, UUID taskId) {
        Subtask subtask = subtaskRepository.findById(subtaskId)
                .orElseThrow(() -> new SubtaskNotFoundException(subtaskId));
        
        if (!subtask.getTaskId().equals(taskId)) {
            throw new ReferentialIntegrityException(
                String.format("Subtask %s does not belong to Task %s", subtaskId, taskId)
            );
        }
    }

    public void validateTaskFormKeyUnique(String formKey, UUID processInstanceId, UUID excludeTaskId) {
        boolean exists;
        if (excludeTaskId != null) {
            exists = taskRepository.findAll().stream()
                    .anyMatch(task -> task.getFormKey().equals(formKey) 
                             && task.getProcessInstanceId().equals(processInstanceId)
                             && !task.getId().equals(excludeTaskId));
        } else {
            exists = taskRepository.existsByFormKeyAndProcessInstanceId(formKey, processInstanceId);
        }
        
        if (exists) {
            throw new DuplicateFormKeyException(formKey, "Task", processInstanceId);
        }
    }

    public void validateSubtaskFormKeyUnique(String formKey, UUID taskId, UUID excludeSubtaskId) {
        boolean exists;
        if (excludeSubtaskId != null) {
            exists = subtaskRepository.findAll().stream()
                    .anyMatch(subtask -> subtask.getFormKey().equals(formKey) 
                             && subtask.getTaskId().equals(taskId)
                             && !subtask.getId().equals(excludeSubtaskId));
        } else {
            exists = subtaskRepository.existsByFormKeyAndTaskId(formKey, taskId);
        }
        
        if (exists) {
            throw new DuplicateFormKeyException(formKey, "Subtask", taskId);
        }
    }

    public void validateTaskOrderUnique(Integer order, UUID processInstanceId, UUID excludeTaskId) {
        boolean exists = taskRepository.findByProcessInstanceId(processInstanceId).stream()
                .anyMatch(task -> task.getOrder().equals(order) 
                         && (excludeTaskId == null || !task.getId().equals(excludeTaskId)));
        
        if (exists) {
            throw new InvalidOrderSequenceException(order, "Task", processInstanceId);
        }
    }

    public void validateSubtaskOrderUnique(Integer order, UUID taskId, UUID excludeSubtaskId) {
        boolean exists = subtaskRepository.findByTaskId(taskId).stream()
                .anyMatch(subtask -> subtask.getOrder().equals(order) 
                         && (excludeSubtaskId == null || !subtask.getId().equals(excludeSubtaskId)));
        
        if (exists) {
            throw new InvalidOrderSequenceException(order, "Subtask", taskId);
        }
    }

    public void validateTaskStateTransition(TaskStatus currentStatus, TaskStatus newStatus) {
        if (!isValidTaskStateTransition(currentStatus, newStatus)) {
            throw new InvalidStateTransitionException(
                currentStatus.toString(), newStatus.toString(), "Task"
            );
        }
    }

    public void validateSubtaskStateTransition(SubtaskStatus currentStatus, SubtaskStatus newStatus) {
        if (!isValidSubtaskStateTransition(currentStatus, newStatus)) {
            throw new InvalidStateTransitionException(
                currentStatus.toString(), newStatus.toString(), "Subtask"
            );
        }
    }

    public void validateProcessStateTransition(ProcessStatus currentStatus, ProcessStatus newStatus) {
        if (!isValidProcessStateTransition(currentStatus, newStatus)) {
            throw new InvalidStateTransitionException(
                currentStatus.toString(), newStatus.toString(), "ProcessInstance"
            );
        }
    }

    public void validateJsonStructure(String json, String fieldName) {
        if (json != null && !json.trim().isEmpty()) {
            try {
                JsonUtils.validateJson(json);
            } catch (InvalidJsonException e) {
                throw new JsonSchemaValidationException(
                    fieldName, "valid JSON", json.length() > 100 ? json.substring(0, 100) + "..." : json
                );
            }
        }
    }

    public void validateUserPermission(Task task, String username) {
        if (task.getAssignedTo() != null && !task.getAssignedTo().equals(username)) {
            throw new TaskNotAssignedException(task.getId(), username);
        }
    }

    public void validateAllSubtasksCompleted(UUID taskId) {
        boolean allCompleted = subtaskRepository.areAllSubtasksCompleted(taskId);
        if (!allCompleted) {
            throw new TaskCannotBeCompletedException(taskId);
        }
    }

    private boolean isValidTaskStateTransition(TaskStatus current, TaskStatus target) {
        return switch (current) {
            case CREATED -> target == TaskStatus.ASSIGNED || target == TaskStatus.IN_PROGRESS || target == TaskStatus.CANCELLED;
            case ASSIGNED -> target == TaskStatus.IN_PROGRESS || target == TaskStatus.CANCELLED;
            case IN_PROGRESS -> target == TaskStatus.COMPLETED || target == TaskStatus.CANCELLED;
            case COMPLETED, CANCELLED -> false; // Terminal states
        };
    }

    private boolean isValidSubtaskStateTransition(SubtaskStatus current, SubtaskStatus target) {
        return switch (current) {
            case CREATED -> target == SubtaskStatus.COMPLETED || target == SubtaskStatus.CANCELLED;
            case COMPLETED, CANCELLED -> false; // Terminal states
        };
    }

    private boolean isValidProcessStateTransition(ProcessStatus current, ProcessStatus target) {
        return switch (current) {
            case RUNNING -> target == ProcessStatus.COMPLETED || target == ProcessStatus.CANCELLED;
            case COMPLETED, CANCELLED -> false; // Terminal states
        };
    }
}
