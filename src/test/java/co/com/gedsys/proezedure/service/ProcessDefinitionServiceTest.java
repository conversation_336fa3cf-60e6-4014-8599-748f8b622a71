package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.TemplateStatus;
import co.com.gedsys.proezedure.exception.DuplicateProcessDefinitionException;
import co.com.gedsys.proezedure.exception.ProcessDefinitionNotFoundException;
import co.com.gedsys.proezedure.repository.ProcessDefinitionRepository;
import co.com.gedsys.proezedure.util.JsonConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.LinkedHashMap;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProcessDefinitionServiceTest {

    @Mock
    private ProcessDefinitionRepository repository;

    @Mock
    private JsonConverter jsonConverter;

    @InjectMocks
    private ProcessDefinitionService service;

    private ProcessDefinition testDefinition;
    private UUID testId;

    @BeforeEach
    void setUp() {
        testId = UUID.randomUUID();
        testDefinition = new ProcessDefinition("Test Process", "Description", "1.0", "{}");
        testDefinition.setId(testId);
    }

    private LinkedHashMap<String, Object> createEmptyVariables() {
        return new LinkedHashMap<>();
    }

    @Test
    void shouldCreateProcessDefinition() {
        // Given
        when(repository.existsByNameAndVersion("Test Process", "1.0")).thenReturn(false);
        when(repository.save(any(ProcessDefinition.class))).thenReturn(testDefinition);
        when(jsonConverter.mapToJsonOrEmpty(any())).thenReturn("{}");

        // When
        ProcessDefinition result = service.create("Test Process", "Description", "1.0", createEmptyVariables());

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Process");
        verify(repository).existsByNameAndVersion("Test Process", "1.0");
        verify(repository).save(any(ProcessDefinition.class));
        verify(jsonConverter).mapToJsonOrEmpty(any());
    }

    @Test
    void shouldThrowExceptionWhenCreatingDuplicateDefinition() {
        // Given
        when(repository.existsByNameAndVersion("Test Process", "1.0")).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> service.create("Test Process", "Description", "1.0", createEmptyVariables()))
                .isInstanceOf(DuplicateProcessDefinitionException.class)
                .hasMessageContaining("Test Process")
                .hasMessageContaining("1.0");

        verify(repository).existsByNameAndVersion("Test Process", "1.0");
        verify(repository, never()).save(any());
    }

    @Test
    void shouldFindProcessDefinitionById() {
        // Given
        when(repository.findById(testId)).thenReturn(Optional.of(testDefinition));

        // When
        ProcessDefinition result = service.findById(testId);

        // Then
        assertThat(result).isEqualTo(testDefinition);
        verify(repository).findById(testId);
    }

    @Test
    void shouldThrowExceptionWhenDefinitionNotFound() {
        // Given
        when(repository.findById(testId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> service.findById(testId))
                .isInstanceOf(ProcessDefinitionNotFoundException.class)
                .hasMessageContaining(testId.toString());

        verify(repository).findById(testId);
    }

    @Test
    void shouldUpdateProcessDefinition() {
        // Given
        when(repository.findById(testId)).thenReturn(Optional.of(testDefinition));
        when(repository.existsByNameAndVersion("Updated Process", "2.0")).thenReturn(false);
        when(repository.save(any(ProcessDefinition.class))).thenReturn(testDefinition);
        when(jsonConverter.mapToJsonOrEmpty(any())).thenReturn("{}");

        // When
        ProcessDefinition result = service.update(testId, "Updated Process", "New Description", "2.0", createEmptyVariables());

        // Then
        assertThat(result).isNotNull();
        verify(repository).findById(testId);
        verify(repository).existsByNameAndVersion("Updated Process", "2.0");
        verify(repository).save(testDefinition);
        verify(jsonConverter).mapToJsonOrEmpty(any());
    }

    @Test
    void shouldUpdateStatusSuccessfully() {
        // Given
        when(repository.findById(testId)).thenReturn(Optional.of(testDefinition));
        when(repository.save(testDefinition)).thenReturn(testDefinition);

        // When
        ProcessDefinition result = service.updateStatus(testId, TemplateStatus.INACTIVE);

        // Then
        assertThat(result.getStatus()).isEqualTo(TemplateStatus.INACTIVE);
        verify(repository).findById(testId);
        verify(repository).save(testDefinition);
    }

    @Test
    void shouldDeleteProcessDefinition() {
        // Given
        when(repository.findById(testId)).thenReturn(Optional.of(testDefinition));

        // When
        service.delete(testId);

        // Then
        verify(repository).findById(testId);
        verify(repository).delete(testDefinition);
    }
}
