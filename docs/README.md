# Documentación del Sistema Proezedure

## 📚 Índice de Documentación

Bienvenido a la documentación completa del Sistema de Gestión de Procesos **Proezedure**. Esta documentación está organizada para diferentes audiencias y casos de uso.

## 🚀 Inicio Rápido

- **[README Principal](../README.md)** - Descripción general, instalación y configuración básica
- **[Guía de Instalación](#instalación)** - Pasos detallados para configurar el entorno

## 📖 Documentación Técnica

### Para Desarrolladores
- **[Guía para Desarrolladores](DEVELOPMENT.md)** - Estándares de código, testing y contribución
- **[Arquitectura del Sistema](ARCHITECTURE.md)** - Diseño técnico detallado y patrones utilizados
- **[Diagramas del Sistema](DIAGRAMS.md)** - Diagramas Mermaid de arquitectura y flujos

### Para Usuarios de API
- **[Documentación de API](API.md)** - Todos los endpoints REST con ejemplos
- **[Casos de Uso](USE_CASES.md)** - Ejemplos prácticos y flujos de trabajo completos
- **[Troubleshooting](TROUBLESHOOTING.md)** - Resolución de problemas comunes

## 🎯 Por Funcionalidad

### Gestión de Plantillas de Proceso
- [Crear plantillas dinámicas](API.md#plantillas-de-proceso)
- [Configurar variables por defecto](USE_CASES.md#variables-de-proceso-complejas)
- [Gestionar versiones](API.md#actualizar-plantilla)

### Gestión de Instancias de Proceso
- [Crear instancias basadas en plantillas](API.md#instancias-de-proceso)
- [Crear instancias independientes](API.md#crear-instancia-sin-plantilla)
- [Gestionar variables JSON](API.md#variables-de-proceso)

### Gestión de Tareas y Subtareas
- [Crear tareas secuenciales](API.md#gestión-de-tareas)
- [Asignar y completar tareas](API.md#asignar-tarea)
- [Gestionar subtareas](API.md#gestión-de-subtareas)
- [Propagación automática de outputs](USE_CASES.md#flujo-completo-proceso-de-aprobación)

### Sistema de Validaciones
- [Validaciones de negocio](ARCHITECTURE.md#validaciones-implementadas)
- [Manejo de excepciones](TROUBLESHOOTING.md#errores-de-validación)
- [Integridad referencial](DEVELOPMENT.md#validaciones)

## 🏗️ Por Rol

### 👨‍💼 Administradores del Sistema
**Configuración y Mantenimiento**
- [Instalación y configuración](../README.md#instalación)
- [Configuración de base de datos](DEVELOPMENT.md#configuración-local)
- [Monitoreo del sistema](TROUBLESHOOTING.md#herramientas-de-diagnóstico)
- [Backup y recuperación](TROUBLESHOOTING.md#base-de-datos)

**Gestión de Plantillas**
- [Crear plantillas de proceso](API.md#crear-plantilla-de-proceso)
- [Configurar variables por defecto](USE_CASES.md#proceso-de-aprobación-de-crédito)
- [Gestionar versiones](API.md#actualizar-plantilla)

### 👨‍💻 Desarrolladores
**Desarrollo y Contribución**
- [Configuración del entorno de desarrollo](DEVELOPMENT.md#configuración-del-entorno)
- [Estándares de código](DEVELOPMENT.md#estándares-de-código)
- [Guía de testing](DEVELOPMENT.md#testing)
- [Proceso de contribución](DEVELOPMENT.md#contribución)

**Arquitectura y Diseño**
- [Arquitectura de capas](ARCHITECTURE.md#arquitectura-de-capas)
- [Patrones de diseño](ARCHITECTURE.md#patrones-de-diseño)
- [Modelo de dominio](ARCHITECTURE.md#modelo-de-dominio)

### 👤 Usuarios de API
**Integración y Uso**
- [Endpoints disponibles](API.md#índice)
- [Ejemplos de uso](API.md#ejemplos-de-uso)
- [Códigos de estado](API.md#códigos-de-estado)
- [Manejo de errores](API.md#validaciones-y-errores)

**Casos de Uso Prácticos**
- [Proceso de crédito completo](USE_CASES.md#flujo-1-proceso-de-crédito-completo)
- [Gestión de solicitudes](USE_CASES.md#proceso-de-aprobación-de-crédito)
- [Onboarding de empleados](USE_CASES.md#proceso-de-onboarding)

### 🔧 Soporte Técnico
**Diagnóstico y Resolución**
- [Problemas comunes](TROUBLESHOOTING.md#problemas-comunes)
- [Errores de base de datos](TROUBLESHOOTING.md#errores-de-base-de-datos)
- [Herramientas de diagnóstico](TROUBLESHOOTING.md#herramientas-de-diagnóstico)
- [Información para reportar bugs](TROUBLESHOOTING.md#soporte)

## 📊 Recursos Visuales

### Diagramas de Arquitectura
- [Diagrama de componentes](DIAGRAMS.md#diagrama-de-componentes)
- [Flujo de datos](DIAGRAMS.md#flujo-de-datos-completo)
- [Arquitectura de despliegue](DIAGRAMS.md#arquitectura-de-despliegue)

### Diagramas de Proceso
- [Estados del sistema](DIAGRAMS.md#estados-del-sistema-detallado)
- [Flujo de variables](DIAGRAMS.md#diagrama-de-flujo-de-variables)
- [Diagramas de secuencia](DIAGRAMS.md#diagramas-de-secuencia)

### Modelo de Datos
- [Entidades y relaciones](../README.md#modelo-de-datos)
- [Estados y transiciones](ARCHITECTURE.md#estados-del-sistema)

## 🔍 Búsqueda Rápida

### Por Funcionalidad
| Funcionalidad | Documentación | API | Ejemplos |
|---------------|---------------|-----|----------|
| **Plantillas de Proceso** | [Arquitectura](ARCHITECTURE.md#modelo-de-dominio) | [API](API.md#plantillas-de-proceso) | [Casos de Uso](USE_CASES.md#proceso-de-aprobación-de-crédito) |
| **Instancias de Proceso** | [Arquitectura](ARCHITECTURE.md#modelo-de-dominio) | [API](API.md#instancias-de-proceso) | [Casos de Uso](USE_CASES.md#flujo-1-proceso-de-crédito-completo) |
| **Tareas** | [Arquitectura](ARCHITECTURE.md#modelo-de-dominio) | [API](API.md#gestión-de-tareas) | [Casos de Uso](USE_CASES.md#paso-3-crear-tareas-secuenciales) |
| **Variables JSON** | [Arquitectura](ARCHITECTURE.md#flujo-de-datos) | [API](API.md#variables-de-proceso) | [Casos de Uso](USE_CASES.md#ejemplos-de-json) |
| **Validaciones** | [Arquitectura](ARCHITECTURE.md#validaciones-implementadas) | [API](API.md#validaciones-y-errores) | [Troubleshooting](TROUBLESHOOTING.md#errores-de-validación) |

### Por Problema
| Problema | Solución |
|----------|----------|
| **Error de conexión a BD** | [Troubleshooting](TROUBLESHOOTING.md#error-de-conexión-a-base-de-datos) |
| **JSON malformado** | [Troubleshooting](TROUBLESHOOTING.md#json-malformado) |
| **FormKey duplicado** | [Troubleshooting](TROUBLESHOOTING.md#formkey-duplicado) |
| **Estado inválido** | [Troubleshooting](TROUBLESHOOTING.md#transición-de-estado-inválida) |
| **Tests fallando** | [Troubleshooting](TROUBLESHOOTING.md#tests-fallando) |

## 📈 Métricas del Sistema

### Cobertura de Documentación
- ✅ **API REST**: 50+ endpoints documentados
- ✅ **Casos de Uso**: 10+ escenarios reales
- ✅ **Diagramas**: 15+ diagramas Mermaid
- ✅ **Troubleshooting**: 20+ problemas comunes

### Cobertura de Testing
- ✅ **Tests Unitarios**: 96 tests pasando
- ✅ **Cobertura de Servicios**: 100%
- ✅ **Cobertura de Validaciones**: 100%
- ✅ **Cobertura de Utilidades**: 100%

## 🔄 Actualizaciones de Documentación

Esta documentación se mantiene actualizada con cada release del sistema. 

**Última actualización**: Enero 2024  
**Versión del sistema**: 1.0.0  
**Tests pasando**: 96/96

## 📞 Soporte y Contacto

- **Equipo de Desarrollo**: Sucomunicación - GEDSYS2
- **Documentación Técnica**: [docs/](.)
- **Reportar Issues**: Contactar al equipo de desarrollo

---

## 🗺️ Mapa de Navegación

```
📁 docs/
├── 📄 README.md (este archivo)
├── 📄 API.md (Documentación completa de API)
├── 📄 ARCHITECTURE.md (Arquitectura técnica)
├── 📄 DEVELOPMENT.md (Guía para desarrolladores)
├── 📄 USE_CASES.md (Casos de uso y ejemplos)
├── 📄 TROUBLESHOOTING.md (Resolución de problemas)
└── 📄 DIAGRAMS.md (Diagramas del sistema)
```

**¡Comienza explorando la documentación que mejor se adapte a tu rol y necesidades!**
