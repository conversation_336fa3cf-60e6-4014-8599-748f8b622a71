/**
 * Utilidades para manipulación del DOM
 */
window.DOMUtils = {
  /**
   * Selecciona un elemento por ID
   */
  getElementById: (id) => document.getElementById(id),

  /**
   * Selecciona elementos por selector CSS
   */
  querySelector: (selector) => document.querySelector(selector),
  querySelectorAll: (selector) => document.querySelectorAll(selector),

  /**
   * Crea un elemento HTML
   */
  createElement: (tag, attributes = {}, content = '') => {
    const element = document.createElement(tag);
    
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'dataset') {
        Object.entries(value).forEach(([dataKey, dataValue]) => {
          element.dataset[dataKey] = dataValue;
        });
      } else {
        element.setAttribute(key, value);
      }
    });
    
    if (content) {
      if (typeof content === 'string') {
        element.innerHTML = content;
      } else {
        element.appendChild(content);
      }
    }
    
    return element;
  },

  /**
   * Muestra/oculta elementos
   */
  show: (element) => {
    if (typeof element === 'string') {
      element = document.getElementById(element);
    }
    if (element) {
      element.classList.remove('hidden');
    }
  },

  hide: (element) => {
    if (typeof element === 'string') {
      element = document.getElementById(element);
    }
    if (element) {
      element.classList.add('hidden');
    }
  },

  /**
   * Alterna la visibilidad de un elemento
   */
  toggle: (element) => {
    if (typeof element === 'string') {
      element = document.getElementById(element);
    }
    if (element) {
      element.classList.toggle('hidden');
    }
  },

  /**
   * Limpia el contenido de un elemento
   */
  clearContent: (element) => {
    if (typeof element === 'string') {
      element = document.getElementById(element);
    }
    if (element) {
      element.innerHTML = '';
    }
  },

  /**
   * Agrega event listeners con cleanup automático
   */
  addEventListener: (element, event, handler, options = {}) => {
    if (typeof element === 'string') {
      element = document.getElementById(element);
    }
    if (element) {
      element.addEventListener(event, handler, options);
      return () => element.removeEventListener(event, handler, options);
    }
    return () => {};
  },

  /**
   * Maneja formularios de manera consistente
   */
  handleForm: (formElement, onSubmit) => {
    if (typeof formElement === 'string') {
      formElement = document.getElementById(formElement);
    }
    
    if (formElement) {
      formElement.addEventListener('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(formElement);
        const data = Object.fromEntries(formData.entries());
        onSubmit(data, formElement);
      });
    }
  },

  /**
   * Actualiza el breadcrumb
   */
  updateBreadcrumb: (items) => {
    const breadcrumb = document.getElementById('breadcrumb');
    if (!breadcrumb) return;

    breadcrumb.innerHTML = '';
    
    items.forEach((item, index) => {
      const li = document.createElement('li');
      li.className = 'breadcrumb-item';
      
      if (index === items.length - 1) {
        li.textContent = item.text;
      } else {
        const a = document.createElement('a');
        a.href = item.href || '#/';
        a.textContent = item.text;
        li.appendChild(a);
      }
      
      breadcrumb.appendChild(li);
    });
  },

  /**
   * Actualiza la navegación activa
   */
  updateActiveNav: (route) => {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.classList.remove('active');
      if (link.dataset.route === route) {
        link.classList.add('active');
      }
    });
  },

  /**
   * Escapa HTML para prevenir XSS
   */
  escapeHtml: (text) => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  /**
   * Valida formularios
   */
  validateForm: (formElement) => {
    if (typeof formElement === 'string') {
      formElement = document.getElementById(formElement);
    }

    if (!formElement) return false;

    const inputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    inputs.forEach(input => {
      const errorElement = input.parentNode.querySelector('.form-error');
      let fieldValid = true;
      let errorMessage = '';

      // Validar campo requerido
      if (!input.value.trim()) {
        fieldValid = false;
        errorMessage = 'Este campo es requerido';
      }
      // Validar tipo de input específico
      else if (input.type === 'email' && !DOMUtils.isValidEmail(input.value)) {
        fieldValid = false;
        errorMessage = 'Formato de email inválido';
      }
      else if (input.type === 'number' && isNaN(input.value)) {
        fieldValid = false;
        errorMessage = 'Debe ser un número válido';
      }
      else if (input.type === 'date' && !DOMUtils.isValidDate(input.value)) {
        fieldValid = false;
        errorMessage = 'Fecha inválida';
      }
      // Validar JSON en textareas con name que contenga 'variables', 'inputs' o 'outputs'
      else if (input.tagName === 'TEXTAREA' &&
               (input.name.includes('variables') || input.name.includes('inputs') || input.name.includes('outputs')) &&
               input.value.trim() && !DOMUtils.isValidJSON(input.value)) {
        fieldValid = false;
        errorMessage = 'Formato JSON inválido';
      }

      if (!fieldValid) {
        isValid = false;
        input.classList.add('error');

        if (!errorElement) {
          const error = document.createElement('div');
          error.className = 'form-error';
          error.textContent = errorMessage;
          input.parentNode.appendChild(error);
        } else {
          errorElement.textContent = errorMessage;
        }
      } else {
        input.classList.remove('error');
        if (errorElement) {
          errorElement.remove();
        }
      }
    });

    return isValid;
  },

  /**
   * Validaciones auxiliares
   */
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidDate: (dateString) => {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  },

  isValidJSON: (jsonString) => {
    try {
      JSON.parse(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  },

  /**
   * Debounce para optimizar eventos
   */
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * Throttle para optimizar eventos
   */
  throttle: (func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};
