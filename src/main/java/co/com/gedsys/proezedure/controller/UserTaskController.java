package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;
import co.com.gedsys.proezedure.dto.TaskResponse;
import co.com.gedsys.proezedure.service.TaskService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/users")
public class UserTaskController {

    private final TaskService taskService;

    public UserTaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @GetMapping("/{username}/tasks")
    public ResponseEntity<List<TaskResponse>> findUserTasks(
            @PathVariable String username,
            @RequestParam(required = false) TaskStatus status) {
        
        List<Task> tasks;
        
        if (status != null) {
            tasks = taskService.findByAssignedToAndStatus(username, status);
        } else {
            // Return all active tasks (assigned, in progress)
            List<TaskStatus> activeStatuses = Arrays.asList(
                TaskStatus.ASSIGNED, 
                TaskStatus.IN_PROGRESS
            );
            tasks = taskService.findByAssignedTo(username).stream()
                    .filter(task -> activeStatuses.contains(task.getStatus()))
                    .collect(Collectors.toList());
        }
        
        List<TaskResponse> responses = tasks.stream()
                .map(TaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{username}/tasks/pending")
    public ResponseEntity<List<TaskResponse>> findPendingTasks(@PathVariable String username) {
        List<Task> tasks = taskService.findByAssignedToAndStatus(username, TaskStatus.ASSIGNED);
        List<TaskResponse> responses = tasks.stream()
                .map(TaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{username}/tasks/in-progress")
    public ResponseEntity<List<TaskResponse>> findInProgressTasks(@PathVariable String username) {
        List<Task> tasks = taskService.findByAssignedToAndStatus(username, TaskStatus.IN_PROGRESS);
        List<TaskResponse> responses = tasks.stream()
                .map(TaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{username}/tasks/completed")
    public ResponseEntity<List<TaskResponse>> findCompletedTasks(@PathVariable String username) {
        List<Task> tasks = taskService.findByAssignedToAndStatus(username, TaskStatus.COMPLETED);
        List<TaskResponse> responses = tasks.stream()
                .map(TaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{username}/tasks/count")
    public ResponseEntity<Long> countUserTasks(
            @PathVariable String username,
            @RequestParam TaskStatus status) {
        
        long count = taskService.countByAssignedToAndStatus(username, status);
        return ResponseEntity.ok(count);
    }
}
