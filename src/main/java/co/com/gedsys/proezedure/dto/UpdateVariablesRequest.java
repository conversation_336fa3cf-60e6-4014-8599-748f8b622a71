package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotNull;
import java.util.LinkedHashMap;

public class UpdateVariablesRequest {

    @NotNull(message = "Variables are required")
    private LinkedHashMap<String, Object> variables;

    public UpdateVariablesRequest() {
    }

    public UpdateVariablesRequest(LinkedHashMap<String, Object> variables) {
        this.variables = variables;
    }

    public LinkedHashMap<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(LinkedHashMap<String, Object> variables) {
        this.variables = variables;
    }
}
