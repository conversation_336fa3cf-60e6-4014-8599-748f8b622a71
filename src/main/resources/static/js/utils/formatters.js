/**
 * Utilidades para formateo de datos
 */
window.Formatters = {
  /**
   * Formatea fechas de manera consistente
   */
  formatDate: (dateString, options = {}) => {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      ...options
    };
    
    return date.toLocaleDateString('es-ES', defaultOptions);
  },

  /**
   * Formatea fecha y hora
   */
  formatDateTime: (dateString, options = {}) => {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    };
    
    return date.toLocaleDateString('es-ES', defaultOptions);
  },

  /**
   * Formatea tiempo relativo (hace X tiempo)
   */
  formatRelativeTime: (dateString) => {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Hace unos segundos';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `Hace ${minutes} minuto${minutes > 1 ? 's' : ''}`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `Hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `Hace ${days} día${days > 1 ? 's' : ''}`;
    } else {
      return Formatters.formatDate(dateString);
    }
  },

  /**
   * Formatea estados con badges
   */
  formatStatus: (status, type = 'default') => {
    if (!status) return '';
    
    const statusMap = {
      // Estados de proceso
      'ACTIVE': { text: 'Activo', class: 'badge-success' },
      'COMPLETED': { text: 'Completado', class: 'badge-primary' },
      'CANCELLED': { text: 'Cancelado', class: 'badge-secondary' },
      'SUSPENDED': { text: 'Suspendido', class: 'badge-warning' },
      
      // Estados de tarea
      'PENDING': { text: 'Pendiente', class: 'badge-warning' },
      'IN_PROGRESS': { text: 'En Progreso', class: 'badge-primary' },
      'CLAIMED': { text: 'Reclamada', class: 'badge-info' },
      
      // Estados de plantilla
      'DRAFT': { text: 'Borrador', class: 'badge-secondary' },
      'PUBLISHED': { text: 'Publicada', class: 'badge-success' },
      'ARCHIVED': { text: 'Archivada', class: 'badge-secondary' }
    };
    
    const statusInfo = statusMap[status] || { text: status, class: 'badge-secondary' };
    return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
  },

  /**
   * Formatea JSON de manera legible
   */
  formatJSON: (jsonString, maxLength = 100) => {
    if (!jsonString) return '-';
    
    try {
      const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString;
      const formatted = JSON.stringify(obj, null, 2);
      
      if (formatted.length > maxLength) {
        return formatted.substring(0, maxLength) + '...';
      }
      
      return formatted;
    } catch (e) {
      return jsonString.toString();
    }
  },

  /**
   * Formatea texto truncándolo si es muy largo
   */
  truncateText: (text, maxLength = 50) => {
    if (!text) return '-';
    
    if (text.length <= maxLength) {
      return text;
    }
    
    return text.substring(0, maxLength) + '...';
  },

  /**
   * Formatea nombres de usuario
   */
  formatUsername: (username) => {
    if (!username) return 'Sin asignar';
    return username;
  },

  /**
   * Formatea números con separadores de miles
   */
  formatNumber: (number) => {
    if (number === null || number === undefined) return '-';
    return number.toLocaleString('es-ES');
  },

  /**
   * Formatea prioridades
   */
  formatPriority: (priority) => {
    const priorityMap = {
      'HIGH': { text: 'Alta', class: 'badge-danger' },
      'MEDIUM': { text: 'Media', class: 'badge-warning' },
      'LOW': { text: 'Baja', class: 'badge-secondary' }
    };
    
    const priorityInfo = priorityMap[priority] || { text: priority || 'Normal', class: 'badge-secondary' };
    return `<span class="badge ${priorityInfo.class}">${priorityInfo.text}</span>`;
  },

  /**
   * Formatea porcentajes de progreso
   */
  formatProgress: (current, total) => {
    if (!total || total === 0) return '0%';
    const percentage = Math.round((current / total) * 100);
    return `${percentage}%`;
  },

  /**
   * Formatea duración en formato legible
   */
  formatDuration: (startDate, endDate) => {
    if (!startDate) return '-';
    
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return '-';
    
    const diffInMs = end - start;
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInHours = Math.floor((diffInMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (diffInDays > 0) {
      return `${diffInDays} día${diffInDays > 1 ? 's' : ''}`;
    } else if (diffInHours > 0) {
      return `${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
    } else {
      const diffInMinutes = Math.floor((diffInMs % (1000 * 60 * 60)) / (1000 * 60));
      return `${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''}`;
    }
  },

  /**
   * Capitaliza la primera letra de una cadena
   */
  capitalize: (str) => {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  },

  /**
   * Convierte camelCase a texto legible
   */
  camelCaseToText: (str) => {
    if (!str) return '';
    return str
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  },

  /**
   * Formatea URLs para mostrar solo el dominio
   */
  formatUrl: (url) => {
    if (!url) return '-';
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (e) {
      return url;
    }
  },

  /**
   * Formatea tamaños de archivo
   */
  formatFileSize: (bytes) => {
    if (!bytes || bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};
