package co.com.gedsys.proezedure.util;

import co.com.gedsys.proezedure.exception.InvalidJsonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Iterator;
import java.util.Map;

public class JsonUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            objectMapper.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    public static void validateJson(String json) {
        if (!isValidJson(json)) {
            throw new InvalidJsonException("Malformed JSON string");
        }
    }

    public static String normalizeJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return "{}";
        }
        
        try {
            JsonNode node = objectMapper.readTree(json);
            return objectMapper.writeValueAsString(node);
        } catch (JsonProcessingException e) {
            throw new InvalidJsonException("Failed to normalize JSON", e);
        }
    }

    public static String mergeJson(String baseJson, String updateJson) {
        if (baseJson == null || baseJson.trim().isEmpty()) {
            baseJson = "{}";
        }
        if (updateJson == null || updateJson.trim().isEmpty()) {
            return baseJson;
        }

        try {
            JsonNode baseNode = objectMapper.readTree(baseJson);
            JsonNode updateNode = objectMapper.readTree(updateJson);
            
            JsonNode mergedNode = merge(baseNode, updateNode);
            return objectMapper.writeValueAsString(mergedNode);
        } catch (JsonProcessingException e) {
            throw new InvalidJsonException("Failed to merge JSON", e);
        }
    }

    public static String patchJson(String baseJson, String patchJson) {
        if (baseJson == null || baseJson.trim().isEmpty()) {
            baseJson = "{}";
        }
        if (patchJson == null || patchJson.trim().isEmpty()) {
            return baseJson;
        }

        try {
            ObjectNode baseNode = (ObjectNode) objectMapper.readTree(baseJson);
            JsonNode patchNode = objectMapper.readTree(patchJson);
            
            if (patchNode.isObject()) {
                Iterator<Map.Entry<String, JsonNode>> fields = patchNode.fields();
                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = fields.next();
                    baseNode.set(field.getKey(), field.getValue());
                }
            }
            
            return objectMapper.writeValueAsString(baseNode);
        } catch (JsonProcessingException e) {
            throw new InvalidJsonException("Failed to patch JSON", e);
        }
    }

    public static String extractFields(String json, String... fieldNames) {
        if (json == null || json.trim().isEmpty()) {
            return "{}";
        }

        try {
            JsonNode sourceNode = objectMapper.readTree(json);
            ObjectNode resultNode = objectMapper.createObjectNode();
            
            for (String fieldName : fieldNames) {
                if (sourceNode.has(fieldName)) {
                    resultNode.set(fieldName, sourceNode.get(fieldName));
                }
            }
            
            return objectMapper.writeValueAsString(resultNode);
        } catch (JsonProcessingException e) {
            throw new InvalidJsonException("Failed to extract fields from JSON", e);
        }
    }

    private static JsonNode merge(JsonNode mainNode, JsonNode updateNode) {
        if (updateNode.isObject() && mainNode.isObject()) {
            ObjectNode merged = (ObjectNode) mainNode.deepCopy();
            Iterator<Map.Entry<String, JsonNode>> fields = updateNode.fields();
            
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String fieldName = field.getKey();
                JsonNode fieldValue = field.getValue();
                
                if (merged.has(fieldName) && merged.get(fieldName).isObject() && fieldValue.isObject()) {
                    merged.set(fieldName, merge(merged.get(fieldName), fieldValue));
                } else {
                    merged.set(fieldName, fieldValue);
                }
            }
            
            return merged;
        } else {
            return updateNode;
        }
    }
}
