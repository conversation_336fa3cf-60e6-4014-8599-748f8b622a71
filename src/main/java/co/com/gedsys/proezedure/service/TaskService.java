package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;
import co.com.gedsys.proezedure.exception.TaskCannotBeCompletedException;
import co.com.gedsys.proezedure.exception.TaskNotAssignedException;
import co.com.gedsys.proezedure.exception.TaskNotFoundException;
import co.com.gedsys.proezedure.repository.SubtaskRepository;
import co.com.gedsys.proezedure.repository.TaskRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class TaskService {

    private final TaskRepository taskRepository;
    private final SubtaskRepository subtaskRepository;
    private final VariableService variableService;
    private final ValidationService validationService;

    public TaskService(TaskRepository taskRepository, SubtaskRepository subtaskRepository,
                      VariableService variableService, ValidationService validationService) {
        this.taskRepository = taskRepository;
        this.subtaskRepository = subtaskRepository;
        this.variableService = variableService;
        this.validationService = validationService;
    }

    public Task create(UUID processInstanceId, String name, String description, String formKey, Integer order, String inputs) {
        // Validate referential integrity
        validationService.validateProcessInstanceExists(processInstanceId);

        // Validate uniqueness constraints
        validationService.validateTaskFormKeyUnique(formKey, processInstanceId, null);
        validationService.validateTaskOrderUnique(order, processInstanceId, null);

        // Validate JSON structure
        validationService.validateJsonStructure(inputs, "inputs");

        Task task = new Task(processInstanceId, name, description, formKey, order, inputs);
        return taskRepository.save(task);
    }

    @Transactional(readOnly = true)
    public Task findById(UUID id) {
        return taskRepository.findById(id)
                .orElseThrow(() -> new TaskNotFoundException(id));
    }

    @Transactional(readOnly = true)
    public Task findByFormKeyAndProcessInstance(String formKey, UUID processInstanceId) {
        return taskRepository.findByFormKeyAndProcessInstanceId(formKey, processInstanceId)
                .orElseThrow(() -> new TaskNotFoundException(formKey));
    }

    @Transactional(readOnly = true)
    public List<Task> findByProcessInstance(UUID processInstanceId) {
        return taskRepository.findByProcessInstanceIdOrderByOrder(processInstanceId);
    }

    @Transactional(readOnly = true)
    public List<Task> findByAssignedTo(String assignedTo) {
        return taskRepository.findByAssignedTo(assignedTo);
    }

    @Transactional(readOnly = true)
    public List<Task> findByAssignedToAndStatus(String assignedTo, TaskStatus status) {
        return taskRepository.findByAssignedToAndStatus(assignedTo, status);
    }

    @Transactional(readOnly = true)
    public List<Task> findAvailableTasks() {
        return taskRepository.findAvailableTasks(TaskStatus.CREATED);
    }

    @Transactional(readOnly = true)
    public List<Task> findByStatus(TaskStatus status) {
        return taskRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<Task> findByFormKey(String formKey) {
        return taskRepository.findByFormKey(formKey);
    }

    public Task assign(UUID taskId, String assignedTo) {
        Task task = findById(taskId);
        task.setAssignedTo(assignedTo);
        return taskRepository.save(task);
    }

    public Task claim(UUID taskId, String username) {
        Task task = findById(taskId);
        
        if (task.getAssignedTo() != null && !task.getAssignedTo().equals(username)) {
            throw new TaskNotAssignedException(taskId, username);
        }
        
        task.claim(username);
        return taskRepository.save(task);
    }

    public Task complete(UUID taskId, String username, String outputs) {
        Task task = findById(taskId);

        // Validate user permissions
        validationService.validateUserPermission(task, username);

        // Validate state transition
        validationService.validateTaskStateTransition(task.getStatus(), TaskStatus.COMPLETED);

        // Validate all subtasks are completed
        validationService.validateAllSubtasksCompleted(taskId);

        // Validate JSON structure
        validationService.validateJsonStructure(outputs, "outputs");

        task.complete(outputs);
        Task savedTask = taskRepository.save(task);

        // Propagate outputs to process variables automatically
        if (outputs != null && !outputs.trim().isEmpty()) {
            try {
                variableService.propagateTaskOutputsWithMapping(
                    task.getProcessInstanceId(),
                    outputs,
                    null // No field mapping, propagate all outputs
                );
            } catch (Exception e) {
                // Log the error but don't fail the task completion
                // In a real implementation, you might want to use a logger here
                System.err.println("Warning: Failed to propagate task outputs to variables: " + e.getMessage());
            }
        }

        return savedTask;
    }

    public Task completeWithSelectivePropagation(UUID taskId, String username, String outputs, String[] outputFields) {
        Task task = findById(taskId);

        if (!task.isAssignedTo(username)) {
            throw new TaskNotAssignedException(taskId, username);
        }

        if (!task.canBeCompleted()) {
            throw new TaskCannotBeCompletedException(taskId);
        }

        task.complete(outputs);
        Task savedTask = taskRepository.save(task);

        // Propagate specific outputs to process variables
        if (outputs != null && !outputs.trim().isEmpty()) {
            try {
                variableService.propagateTaskOutputsWithMapping(
                    task.getProcessInstanceId(),
                    outputs,
                    outputFields
                );
            } catch (Exception e) {
                // Log the error but don't fail the task completion
                System.err.println("Warning: Failed to propagate task outputs to variables: " + e.getMessage());
            }
        }

        return savedTask;
    }

    public Task updateStatus(UUID taskId, TaskStatus status) {
        Task task = findById(taskId);

        // Validate state transition
        validationService.validateTaskStateTransition(task.getStatus(), status);

        task.setStatus(status);
        return taskRepository.save(task);
    }

    public Task updateInputs(UUID taskId, String inputs) {
        Task task = findById(taskId);
        task.setInputs(inputs);
        return taskRepository.save(task);
    }

    public Task updateOutputs(UUID taskId, String outputs) {
        Task task = findById(taskId);
        task.setOutputs(outputs);
        return taskRepository.save(task);
    }

    public Task update(UUID taskId, String name, String description, String formKey, Integer order, String inputs) {
        Task task = findById(taskId);

        // Validate uniqueness constraints if values changed
        if (!task.getFormKey().equals(formKey)) {
            validationService.validateTaskFormKeyUnique(formKey, task.getProcessInstanceId(), taskId);
        }
        if (!task.getOrder().equals(order)) {
            validationService.validateTaskOrderUnique(order, task.getProcessInstanceId(), taskId);
        }

        // Validate JSON structure
        validationService.validateJsonStructure(inputs, "inputs");

        task.setName(name);
        task.setDescription(description);
        task.setFormKey(formKey);
        task.setOrder(order);
        task.setInputs(inputs);
        return taskRepository.save(task);
    }

    public void delete(UUID taskId) {
        Task task = findById(taskId);
        taskRepository.delete(task);
    }

    @Transactional(readOnly = true)
    public long countByProcessInstanceAndStatus(UUID processInstanceId, TaskStatus status) {
        return taskRepository.countByProcessInstanceIdAndStatus(processInstanceId, status);
    }

    @Transactional(readOnly = true)
    public long countByAssignedToAndStatus(String assignedTo, TaskStatus status) {
        return taskRepository.countByAssignedToAndStatus(assignedTo, status);
    }

    @Transactional(readOnly = true)
    public boolean existsByFormKeyAndProcessInstance(String formKey, UUID processInstanceId) {
        return taskRepository.existsByFormKeyAndProcessInstanceId(formKey, processInstanceId);
    }

    @Transactional(readOnly = true)
    public Task findNextTaskInProcess(UUID processInstanceId) {
        return taskRepository.findNextTaskInProcess(processInstanceId).orElse(null);
    }
}
