# Changelog - Fecha de Vencimiento para Procesos

## Resumen de Cambios

Se ha agregado la funcionalidad de fecha de vencimiento (`dueDate`) a los procesos del sistema. Esta fecha representa el día, mes y año límite para completar un proceso.

## Archivos Modificados

### Entidades de Dominio

1. **ProcessInstance.java**
   - Agregado campo `dueDate` de tipo `LocalDate`
   - Agregados constructores que incluyen `dueDate`
   - Agregados getters y setters para `dueDate`

### DTOs

2. **ProcessInstanceResponse.java**
   - Agregado campo `dueDate` de tipo `LocalDate`
   - Actualizado constructor y método `from()` para incluir `dueDate`
   - Agregados getters y setters para `dueDate`

3. **CreateProcessInstanceRequest.java**
   - Agregado campo `dueDate` de tipo `LocalDate`
   - Agregados constructores que incluyen `dueDate`
   - Agregados getters y setters para `dueDate`

### Servicios

4. **ProcessInstanceService.java**
   - Agregados métodos sobrecargados para crear procesos con `dueDate`:
     - `createFromTemplate()` con parámetro `dueDate`
     - `createAdHoc()` con parámetro `dueDate`
     - `update()` con parámetro `dueDate`

### Controladores

5. **ProcessInstanceController.java**
   - Actualizado método `create()` para manejar `dueDate` opcional
   - Actualizado método `update()` para manejar `dueDate` opcional
   - Actualizado método `createInstanceFromTemplate()` para manejar `dueDate` opcional

### Base de Datos

6. **add_due_date_column.sql**
   - Script SQL para agregar la columna `due_date` a la tabla `process_instances`

### Pruebas

7. **ProcessInstanceRepositoryTest.java**
   - Agregado test `shouldSaveAndRetrieveProcessInstanceWithDueDate()` para verificar funcionalidad

### Documentación

8. **README.md**
   - Actualizado diagrama ER para incluir campo `dueDate`
   - Agregados ejemplos de uso con fecha de vencimiento

9. **docs/ARCHITECTURE.md**
   - Actualizado diagrama de clases para incluir campo `dueDate`

## Características Implementadas

- ✅ Campo `dueDate` opcional en procesos
- ✅ Soporte para crear procesos con fecha de vencimiento
- ✅ Soporte para actualizar fecha de vencimiento de procesos existentes
- ✅ Compatibilidad hacia atrás (procesos sin fecha de vencimiento siguen funcionando)
- ✅ Validación automática de tipos de datos
- ✅ Pruebas unitarias para verificar funcionalidad
- ✅ Documentación actualizada

## Uso

### Crear Proceso con Fecha de Vencimiento

```json
{
  "name": "Proceso de Aprobación",
  "description": "Proceso que debe completarse antes del fin de año",
  "dueDate": "2025-12-31",
  "variables": {
    "clienteId": "12345",
    "montoSolicitud": 50000
  }
}
```

### Crear Proceso sin Fecha de Vencimiento

```json
{
  "name": "Proceso sin Fecha Límite",
  "description": "Proceso que no tiene fecha de vencimiento",
  "variables": {
    "clienteId": "67890"
  }
}
```

## Migración de Base de Datos

Para aplicar los cambios en una base de datos existente, ejecutar:

```sql
-- Agregar columna due_date a la tabla process_instances
ALTER TABLE process_instances 
ADD COLUMN due_date DATE;

-- Agregar comentario para documentar la columna
COMMENT ON COLUMN process_instances.due_date IS 'Fecha de vencimiento del proceso (día, mes, año)';
```

## Notas Técnicas

- El campo `dueDate` es opcional (puede ser `null`)
- Se usa `LocalDate` para representar solo fecha (sin hora)
- La columna en base de datos es de tipo `DATE`
- Los procesos existentes mantendrán `dueDate` como `null`
- La funcionalidad es completamente compatible hacia atrás
