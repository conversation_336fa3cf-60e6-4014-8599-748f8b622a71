/**
 * Componente Process Definitions - Gestión de plantillas de proceso
 */
window.ProcessDefinitionsComponent = {
  name: 'process-definitions',
  currentDefinitions: [],
  
  async render() {
    try {
      DOMUtils.show('loading');
      
      // Obtener plantillas de proceso
      this.currentDefinitions = await API.processDefinitions.getAll();
      
      DOMUtils.hide('loading');
      
      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Inicio', href: '#/' },
        { text: 'Plantillas de Proceso' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate();

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando plantillas de proceso');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate() {
    return `
      <div class="process-definitions">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Plantillas de Proceso</h1>
              <p class="text-gray-600">Gestiona las plantillas base para crear nuevos procesos</p>
            </div>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Nueva Plantilla
            </button>
          </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-3">
              <div class="form-group">
                <label class="form-label">Buscar</label>
                <input type="text" class="form-input" id="search-input" placeholder="Buscar por nombre...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Estado</label>
                <select class="form-select" id="status-filter">
                  <option value="">Todos los estados</option>
                  <option value="ACTIVE">Activo</option>
                  <option value="DRAFT">Borrador</option>
                  <option value="ARCHIVED">Archivado</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Ordenar por</label>
                <select class="form-select" id="sort-select">
                  <option value="name">Nombre</option>
                  <option value="createdAt">Fecha de creación</option>
                  <option value="status">Estado</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de plantillas -->
        <div class="definitions-list" id="definitions-list">
          ${this.getDefinitionsListTemplate()}
        </div>
      </div>

      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .definition-card {
          background: var(--color-white);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius-lg);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          transition: var(--transition);
        }
        
        .definition-card:hover {
          box-shadow: var(--shadow-md);
        }
        
        .definition-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-3);
        }
        
        .definition-title {
          font-size: var(--font-size-lg);
          font-weight: 600;
          color: var(--color-gray-800);
          margin-bottom: var(--spacing-1);
        }
        
        .definition-meta {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
        }
        
        .definition-actions {
          display: flex;
          gap: var(--spacing-2);
        }
        
        .definition-description {
          color: var(--color-gray-600);
          margin-bottom: var(--spacing-3);
        }
        
        .definition-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: var(--spacing-3);
          border-top: 1px solid var(--color-gray-200);
        }
      </style>
    `;
  },

  getDefinitionsListTemplate() {
    if (this.currentDefinitions.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <h3>No hay plantillas de proceso</h3>
            <p class="text-gray-600 mb-4">Crea tu primera plantilla para comenzar a gestionar procesos.</p>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Crear Primera Plantilla
            </button>
          </div>
        </div>
      `;
    }

    return this.currentDefinitions.map(definition => `
      <div class="definition-card" data-id="${definition.id}">
        <div class="definition-header">
          <div>
            <h3 class="definition-title">${DOMUtils.escapeHtml(definition.name)}</h3>
            <div class="definition-meta">
              Versión ${definition.version} • 
              Creado ${Formatters.formatRelativeTime(definition.createdAt)}
            </div>
          </div>
          <div class="definition-actions">
            <button class="btn btn-sm btn-outline" data-action="edit" data-id="${definition.id}">
              Editar
            </button>
            <button class="btn btn-sm btn-secondary" data-action="toggle-status" data-id="${definition.id}">
              ${definition.status === 'ACTIVE' ? 'Desactivar' : 'Activar'}
            </button>
            <button class="btn btn-sm btn-primary" data-action="create-instance" data-id="${definition.id}">
              Crear Proceso
            </button>
            <button class="btn btn-sm btn-outline" data-action="delete" data-id="${definition.id}" style="color: var(--color-gray-700);">
              Eliminar
            </button>
          </div>
        </div>
        
        ${definition.description ? `<div class="definition-description">${DOMUtils.escapeHtml(definition.description)}</div>` : ''}
        
        <div class="definition-footer">
          <div>
            ${Formatters.formatStatus(definition.status)}
          </div>
          <div class="definition-meta">
            ${definition.defaultVariables ? 'Con variables por defecto' : 'Sin variables por defecto'}
          </div>
        </div>
      </div>
    `).join('');
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar plantillas</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener las plantillas de proceso.</p>
          <button class="btn btn-primary" onclick="window.ProcessDefinitionsComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  setupEventListeners() {
    // Búsqueda
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
      searchInput.addEventListener('input', DOMUtils.debounce((e) => {
        this.filterDefinitions();
      }, 300));
    }

    // Filtros
    const statusFilter = document.getElementById('status-filter');
    const sortSelect = document.getElementById('sort-select');
    
    if (statusFilter) {
      statusFilter.addEventListener('change', () => this.filterDefinitions());
    }
    
    if (sortSelect) {
      sortSelect.addEventListener('change', () => this.filterDefinitions());
    }

    // Acciones de botones
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const id = actionButton.dataset.id;

      this.handleAction(action, id);
    });
  },

  filterDefinitions() {
    const searchTerm = document.getElementById('search-input')?.value.toLowerCase() || '';
    const statusFilter = document.getElementById('status-filter')?.value || '';
    const sortBy = document.getElementById('sort-select')?.value || 'name';

    let filtered = [...this.currentDefinitions];

    // Filtrar por búsqueda
    if (searchTerm) {
      filtered = filtered.filter(def => 
        def.name.toLowerCase().includes(searchTerm) ||
        (def.description && def.description.toLowerCase().includes(searchTerm))
      );
    }

    // Filtrar por estado
    if (statusFilter) {
      filtered = filtered.filter(def => def.status === statusFilter);
    }

    // Ordenar
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'createdAt':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    // Actualizar la lista
    const listContainer = document.getElementById('definitions-list');
    if (listContainer) {
      const originalDefinitions = this.currentDefinitions;
      this.currentDefinitions = filtered;
      listContainer.innerHTML = this.getDefinitionsListTemplate();
      this.currentDefinitions = originalDefinitions;
    }
  },

  async handleAction(action, id) {
    try {
      switch (action) {
        case 'create':
          this.showCreateModal();
          break;
        case 'edit':
          this.showEditModal(id);
          break;
        case 'toggle-status':
          await this.toggleDefinitionStatus(id);
          break;
        case 'create-instance':
          this.createInstanceFromDefinition(id);
          break;
        case 'delete':
          await this.deleteDefinition(id);
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  showCreateModal() {
    const modalContent = this.getCreateModalTemplate();
    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="save-definition-btn">Crear Plantilla</button>
    `;

    ModalManager.show('Nueva Plantilla de Proceso', modalContent, modalFooter);

    // Configurar event listeners del modal
    this.setupCreateModalListeners();
  },

  showEditModal(id) {
    const definition = this.currentDefinitions.find(def => def.id === id);
    if (!definition) {
      window.NotificationManager.error('Plantilla no encontrada');
      return;
    }

    const modalContent = this.getEditModalTemplate(definition);
    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="update-definition-btn">Actualizar Plantilla</button>
    `;

    ModalManager.show('Editar Plantilla de Proceso', modalContent, modalFooter);

    // Configurar event listeners del modal
    this.setupEditModalListeners(id);
  },

  async toggleDefinitionStatus(id) {
    const definition = this.currentDefinitions.find(def => def.id === id);
    if (!definition) {
      window.NotificationManager.error('Plantilla no encontrada');
      return;
    }

    const newStatus = definition.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const action = newStatus === 'ACTIVE' ? 'activar' : 'desactivar';

    if (confirm(`¿Estás seguro de que quieres ${action} esta plantilla?`)) {
      try {
        await API.processDefinitions.updateStatus(id, newStatus);
        window.NotificationManager.success(`Plantilla ${action === 'activar' ? 'activada' : 'desactivada'} exitosamente`);
        await this.render(); // Recargar la lista
      } catch (error) {
        API.utils.handleError(error, `${action} plantilla`);
      }
    }
  },

  async deleteDefinition(id) {
    const definition = this.currentDefinitions.find(def => def.id === id);
    if (!definition) {
      window.NotificationManager.error('Plantilla no encontrada');
      return;
    }

    if (confirm(`¿Estás seguro de que quieres eliminar la plantilla "${definition.name}"? Esta acción no se puede deshacer.`)) {
      try {
        await API.processDefinitions.delete(id);
        window.NotificationManager.success('Plantilla eliminada exitosamente');
        await this.render(); // Recargar la lista
      } catch (error) {
        API.utils.handleError(error, 'eliminando plantilla');
      }
    }
  },

  createInstanceFromDefinition(id) {
    // Navegar a creación de instancia con plantilla preseleccionada
    if (window.Router) {
      window.Router.navigate(`/process-instances?template=${id}`);
    } else {
      window.location.hash = `#/process-instances?template=${id}`;
    }
  },

  // ===== TEMPLATES DE MODALES =====

  getCreateModalTemplate() {
    return `
      <form id="create-definition-form" class="definition-form">
        <div class="form-group">
          <label class="form-label" for="definition-name">Nombre *</label>
          <input type="text" class="form-input" id="definition-name" name="name" required
                 placeholder="Nombre de la plantilla">
        </div>

        <div class="form-group">
          <label class="form-label" for="definition-description">Descripción</label>
          <textarea class="form-textarea" id="definition-description" name="description"
                    placeholder="Descripción de la plantilla" rows="3"></textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="definition-version">Versión *</label>
          <input type="text" class="form-input" id="definition-version" name="version" required
                 placeholder="1.0.0" value="1.0.0">
        </div>

        <div class="form-group">
          <label class="form-label" for="definition-status">Estado</label>
          <select class="form-select" id="definition-status" name="status">
            <option value="DRAFT">Borrador</option>
            <option value="ACTIVE" selected>Activo</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label" for="definition-variables">Variables por Defecto (JSON)</label>
          <textarea class="form-textarea" id="definition-variables" name="defaultVariables"
                    placeholder='{"variable1": "valor1", "variable2": "valor2"}' rows="4"></textarea>
          <small class="form-help">Formato JSON válido. Opcional.</small>
        </div>
      </form>
    `;
  },

  getEditModalTemplate(definition) {
    const defaultVariables = definition.defaultVariables ?
      (typeof definition.defaultVariables === 'string' ?
        definition.defaultVariables :
        JSON.stringify(definition.defaultVariables, null, 2)) : '';

    return `
      <form id="edit-definition-form" class="definition-form">
        <div class="form-group">
          <label class="form-label" for="edit-definition-name">Nombre *</label>
          <input type="text" class="form-input" id="edit-definition-name" name="name" required
                 value="${DOMUtils.escapeHtml(definition.name)}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-definition-description">Descripción</label>
          <textarea class="form-textarea" id="edit-definition-description" name="description"
                    rows="3">${DOMUtils.escapeHtml(definition.description || '')}</textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-definition-version">Versión *</label>
          <input type="text" class="form-input" id="edit-definition-version" name="version" required
                 value="${DOMUtils.escapeHtml(definition.version)}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-definition-status">Estado</label>
          <select class="form-select" id="edit-definition-status" name="status">
            <option value="DRAFT" ${definition.status === 'DRAFT' ? 'selected' : ''}>Borrador</option>
            <option value="ACTIVE" ${definition.status === 'ACTIVE' ? 'selected' : ''}>Activo</option>
            <option value="ARCHIVED" ${definition.status === 'ARCHIVED' ? 'selected' : ''}>Archivado</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-definition-variables">Variables por Defecto (JSON)</label>
          <textarea class="form-textarea" id="edit-definition-variables" name="defaultVariables"
                    rows="4">${DOMUtils.escapeHtml(defaultVariables)}</textarea>
          <small class="form-help">Formato JSON válido. Opcional.</small>
        </div>
      </form>
    `;
  },

  // ===== EVENT LISTENERS DE MODALES =====

  setupCreateModalListeners() {
    const saveBtn = document.getElementById('save-definition-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', async () => {
        await this.handleCreateSubmit();
      });
    }
  },

  setupEditModalListeners(id) {
    const updateBtn = document.getElementById('update-definition-btn');
    if (updateBtn) {
      updateBtn.addEventListener('click', async () => {
        await this.handleEditSubmit(id);
      });
    }
  },

  async handleCreateSubmit() {
    const form = document.getElementById('create-definition-form');
    if (!form || !DOMUtils.validateForm(form)) {
      return;
    }

    try {
      const formData = new FormData(form);
      const data = {
        name: formData.get('name'),
        description: formData.get('description') || null,
        version: formData.get('version'),
        status: formData.get('status'),
        defaultVariables: this.parseJsonField(formData.get('defaultVariables'))
      };

      await API.processDefinitions.create(data);

      ModalManager.hide();
      window.NotificationManager.success('Plantilla creada exitosamente');
      await this.render(); // Recargar la lista

    } catch (error) {
      API.utils.handleError(error, 'creando plantilla');
    }
  },

  async handleEditSubmit(id) {
    const form = document.getElementById('edit-definition-form');
    if (!form || !DOMUtils.validateForm(form)) {
      return;
    }

    try {
      const formData = new FormData(form);
      const data = {
        name: formData.get('name'),
        description: formData.get('description') || null,
        version: formData.get('version'),
        status: formData.get('status'),
        defaultVariables: this.parseJsonField(formData.get('defaultVariables'))
      };

      await API.processDefinitions.update(id, data);

      ModalManager.hide();
      window.NotificationManager.success('Plantilla actualizada exitosamente');
      await this.render(); // Recargar la lista

    } catch (error) {
      API.utils.handleError(error, 'actualizando plantilla');
    }
  },

  parseJsonField(jsonString) {
    if (!jsonString || !jsonString.trim()) {
      return null;
    }

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      throw new Error('El formato JSON no es válido');
    }
  }
};
