/**
 * Componente Tasks - Gestión de tareas
 */
window.TasksComponent = {
  name: 'tasks',
  currentTasks: [],
  
  async render() {
    try {
      DOMUtils.show('loading');
      
      // Obtener tareas
      this.currentTasks = await API.tasks.getAll();
      
      DOMUtils.hide('loading');
      
      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Inicio', href: '#/' },
        { text: 'Tareas' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate();

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando tareas');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate() {
    return `
      <div class="tasks">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Tareas</h1>
              <p class="text-gray-600">Gestiona todas las tareas del sistema</p>
            </div>
            <div class="flex gap-2">
              <button class="btn btn-secondary" data-action="view-available">
                Tareas Disponibles
              </button>
              <button class="btn btn-primary" data-action="create">
                <span>➕</span>
                Nueva Tarea
              </button>
            </div>
          </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-4">
              <div class="form-group">
                <label class="form-label">Buscar</label>
                <input type="text" class="form-input" id="search-input" placeholder="Buscar por nombre...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Estado</label>
                <select class="form-select" id="status-filter">
                  <option value="">Todos los estados</option>
                  <option value="PENDING">Pendiente</option>
                  <option value="IN_PROGRESS">En Progreso</option>
                  <option value="COMPLETED">Completada</option>
                  <option value="CLAIMED">Reclamada</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Asignado a</label>
                <input type="text" class="form-input" id="assignee-filter" placeholder="Usuario asignado...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Ordenar por</label>
                <select class="form-select" id="sort-select">
                  <option value="order">Orden</option>
                  <option value="name">Nombre</option>
                  <option value="status">Estado</option>
                  <option value="assignedTo">Asignado a</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de tareas -->
        <div class="tasks-list" id="tasks-list">
          ${this.getTasksListTemplate()}
        </div>
      </div>

      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .gap-2 {
          gap: var(--spacing-2);
        }
        
        .task-card {
          background: var(--color-white);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius-lg);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          transition: var(--transition);
        }
        
        .task-card:hover {
          box-shadow: var(--shadow-md);
        }
        
        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-3);
        }
        
        .task-title {
          font-size: var(--font-size-lg);
          font-weight: 600;
          color: var(--color-gray-800);
          margin-bottom: var(--spacing-1);
        }
        
        .task-meta {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
        }
        
        .task-actions {
          display: flex;
          gap: var(--spacing-2);
        }
        
        .task-description {
          color: var(--color-gray-600);
          margin-bottom: var(--spacing-3);
        }
        
        .task-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: var(--spacing-3);
          border-top: 1px solid var(--color-gray-200);
        }
        
        .task-order {
          background-color: var(--color-gray-100);
          color: var(--color-gray-700);
          padding: var(--spacing-1) var(--spacing-2);
          border-radius: var(--border-radius);
          font-size: var(--font-size-sm);
          font-weight: 500;
        }
      </style>
    `;
  },

  getTasksListTemplate() {
    if (this.currentTasks.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <h3>No hay tareas</h3>
            <p class="text-gray-600 mb-4">No se encontraron tareas en el sistema.</p>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Crear Primera Tarea
            </button>
          </div>
        </div>
      `;
    }

    return this.currentTasks.map(task => `
      <div class="task-card" data-id="${task.id}">
        <div class="task-header">
          <div>
            <h3 class="task-title">${DOMUtils.escapeHtml(task.name)}</h3>
            <div class="task-meta">
              <span class="task-order">Orden ${task.order}</span>
              ${task.formKey ? ` • Form: ${task.formKey}` : ''}
            </div>
          </div>
          <div class="task-actions">
            ${task.status === 'PENDING' && !task.assignedTo ? 
              `<button class="btn btn-sm btn-primary" data-action="claim" data-id="${task.id}">Reclamar</button>` : 
              ''
            }
            ${task.status === 'IN_PROGRESS' ? 
              `<button class="btn btn-sm btn-success" data-action="complete" data-id="${task.id}">Completar</button>` : 
              ''
            }
            <button class="btn btn-sm btn-outline" data-action="view-subtasks" data-id="${task.id}">
              Subtareas
            </button>
            <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${task.id}">
              Editar
            </button>
            ${!task.assignedTo ?
              `<button class="btn btn-sm btn-outline" data-action="assign" data-id="${task.id}">Asignar</button>` :
              ''
            }
            <button class="btn btn-sm btn-outline" data-action="delete" data-id="${task.id}" style="color: var(--color-gray-700);">
              Eliminar
            </button>
          </div>
        </div>
        
        ${task.description ? `<div class="task-description">${DOMUtils.escapeHtml(task.description)}</div>` : ''}
        
        <div class="task-footer">
          <div>
            ${Formatters.formatStatus(task.status)}
          </div>
          <div class="task-meta">
            ${task.assignedTo ? `Asignada a: ${Formatters.formatUsername(task.assignedTo)}` : 'Sin asignar'}
          </div>
        </div>
      </div>
    `).join('');
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar tareas</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener las tareas.</p>
          <button class="btn btn-primary" onclick="window.TasksComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  setupEventListeners() {
    // Búsqueda y filtros
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const assigneeFilter = document.getElementById('assignee-filter');
    const sortSelect = document.getElementById('sort-select');
    
    if (searchInput) {
      searchInput.addEventListener('input', DOMUtils.debounce(() => this.filterTasks(), 300));
    }
    
    [statusFilter, assigneeFilter, sortSelect].forEach(element => {
      if (element) {
        element.addEventListener('change', () => this.filterTasks());
      }
    });

    // Acciones de botones
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const id = actionButton.dataset.id;

      this.handleAction(action, id);
    });
  },

  filterTasks() {
    console.log('Filtrar tareas');
  },

  async handleAction(action, id) {
    try {
      switch (action) {
        case 'create':
          this.showCreateModal();
          break;
        case 'view-available':
          await this.loadAvailableTasks();
          break;
        case 'claim':
          await this.claimTask(id);
          break;
        case 'complete':
          await this.completeTask(id);
          break;
        case 'view-subtasks':
          this.viewSubtasks(id);
          break;
        case 'assign':
          await this.assignTask(id);
          break;
        case 'edit':
          this.showEditModal(id);
          break;
        case 'delete':
          await this.deleteTask(id);
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  async showCreateModal() {
    try {
      // Obtener procesos activos para el selector
      const processes = await API.processInstances.getAll({ status: 'ACTIVE' });

      const modalContent = this.getCreateModalTemplate(processes);
      const modalFooter = `
        <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
        <button type="button" class="btn btn-primary" id="save-task-btn">Crear Tarea</button>
      `;

      ModalManager.show('Nueva Tarea', modalContent, modalFooter);

      // Configurar event listeners del modal
      this.setupCreateModalListeners();

    } catch (error) {
      API.utils.handleError(error, 'cargando procesos');
    }
  },

  async loadAvailableTasks() {
    try {
      DOMUtils.show('loading');
      this.currentTasks = await API.tasks.getAvailable();
      DOMUtils.hide('loading');
      
      const listContainer = document.getElementById('tasks-list');
      if (listContainer) {
        listContainer.innerHTML = this.getTasksListTemplate();
      }
    } catch (error) {
      DOMUtils.hide('loading');
      throw error;
    }
  },

  async claimTask(id) {
    const username = prompt('Ingresa tu nombre de usuario:');
    if (!username) return;
    
    await API.tasks.claim(id, username);
    await this.render(); // Recargar la vista
  },

  async completeTask(id) {
    if (confirm('¿Estás seguro de que quieres completar esta tarea?')) {
      await API.tasks.complete(id);
      await this.render(); // Recargar la vista
    }
  },

  viewSubtasks(id) {
    // Navegar a gestión de subtareas
    if (window.Router) {
      window.Router.navigate(`/subtasks?task=${id}`);
    } else {
      window.location.hash = `#/subtasks?task=${id}`;
    }
  },

  async assignTask(id) {
    const username = prompt('Ingresa el nombre de usuario para asignar la tarea:');
    if (!username) return;

    try {
      await API.tasks.assign(id, username);
      window.NotificationManager.success('Tarea asignada exitosamente');
      await this.render(); // Recargar la lista
    } catch (error) {
      API.utils.handleError(error, 'asignando tarea');
    }
  },

  async deleteTask(id) {
    const task = this.currentTasks.find(t => t.id === id);
    if (!task) {
      window.NotificationManager.error('Tarea no encontrada');
      return;
    }

    if (confirm(`¿Estás seguro de que quieres eliminar la tarea "${task.name}"? Esta acción no se puede deshacer.`)) {
      try {
        await API.tasks.delete(id);
        window.NotificationManager.success('Tarea eliminada exitosamente');
        await this.render(); // Recargar la lista
      } catch (error) {
        API.utils.handleError(error, 'eliminando tarea');
      }
    }
  },

  showEditModal(id) {
    const task = this.currentTasks.find(t => t.id === id);
    if (!task) {
      window.NotificationManager.error('Tarea no encontrada');
      return;
    }

    const modalContent = this.getEditModalTemplate(task);
    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="update-task-btn">Actualizar Tarea</button>
    `;

    ModalManager.show('Editar Tarea', modalContent, modalFooter);

    // Configurar event listeners del modal
    this.setupEditModalListeners(id);
  },

  // ===== TEMPLATES DE MODALES =====

  getCreateModalTemplate(processes = []) {
    const processOptions = processes.map(process =>
      `<option value="${process.id}">${DOMUtils.escapeHtml(process.name)}</option>`
    ).join('');

    return `
      <form id="create-task-form" class="task-form">
        <div class="form-group">
          <label class="form-label" for="task-process">Proceso *</label>
          <select class="form-select" id="task-process" name="processInstanceId" required>
            <option value="">Seleccionar proceso</option>
            ${processOptions}
          </select>
        </div>

        <div class="form-group">
          <label class="form-label" for="task-name">Nombre *</label>
          <input type="text" class="form-input" id="task-name" name="name" required
                 placeholder="Nombre de la tarea">
        </div>

        <div class="form-group">
          <label class="form-label" for="task-description">Descripción</label>
          <textarea class="form-textarea" id="task-description" name="description"
                    placeholder="Descripción de la tarea" rows="3"></textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="task-form-key">Form Key *</label>
          <input type="text" class="form-input" id="task-form-key" name="formKey" required
                 placeholder="form_key_unique">
          <small class="form-help">Identificador único del formulario asociado a la tarea.</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="task-order">Orden *</label>
          <input type="number" class="form-input" id="task-order" name="order" required
                 min="1" value="1">
          <small class="form-help">Orden de ejecución de la tarea en el proceso.</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="task-assigned-to">Asignado a</label>
          <input type="text" class="form-input" id="task-assigned-to" name="assignedTo"
                 placeholder="<EMAIL>">
          <small class="form-help">Usuario responsable de la tarea. Opcional.</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="task-inputs">Inputs (JSON)</label>
          <textarea class="form-textarea" id="task-inputs" name="inputs"
                    placeholder='{"input1": "valor1", "input2": "valor2"}' rows="3"></textarea>
          <small class="form-help">Datos de entrada para la tarea en formato JSON.</small>
        </div>
      </form>
    `;
  },

  getEditModalTemplate(task) {
    const inputs = task.inputs ?
      (typeof task.inputs === 'string' ?
        task.inputs :
        JSON.stringify(task.inputs, null, 2)) : '';

    const outputs = task.outputs ?
      (typeof task.outputs === 'string' ?
        task.outputs :
        JSON.stringify(task.outputs, null, 2)) : '';

    return `
      <form id="edit-task-form" class="task-form">
        <div class="form-group">
          <label class="form-label" for="edit-task-name">Nombre *</label>
          <input type="text" class="form-input" id="edit-task-name" name="name" required
                 value="${DOMUtils.escapeHtml(task.name)}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-task-description">Descripción</label>
          <textarea class="form-textarea" id="edit-task-description" name="description"
                    rows="3">${DOMUtils.escapeHtml(task.description || '')}</textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-task-form-key">Form Key *</label>
          <input type="text" class="form-input" id="edit-task-form-key" name="formKey" required
                 value="${DOMUtils.escapeHtml(task.formKey)}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-task-order">Orden *</label>
          <input type="number" class="form-input" id="edit-task-order" name="order" required
                 min="1" value="${task.order}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-task-assigned-to">Asignado a</label>
          <input type="text" class="form-input" id="edit-task-assigned-to" name="assignedTo"
                 value="${DOMUtils.escapeHtml(task.assignedTo || '')}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-task-status">Estado</label>
          <select class="form-select" id="edit-task-status" name="status">
            <option value="PENDING" ${task.status === 'PENDING' ? 'selected' : ''}>Pendiente</option>
            <option value="IN_PROGRESS" ${task.status === 'IN_PROGRESS' ? 'selected' : ''}>En Progreso</option>
            <option value="COMPLETED" ${task.status === 'COMPLETED' ? 'selected' : ''}>Completada</option>
            <option value="CLAIMED" ${task.status === 'CLAIMED' ? 'selected' : ''}>Reclamada</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-task-inputs">Inputs (JSON)</label>
          <textarea class="form-textarea" id="edit-task-inputs" name="inputs"
                    rows="3">${DOMUtils.escapeHtml(inputs)}</textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-task-outputs">Outputs (JSON)</label>
          <textarea class="form-textarea" id="edit-task-outputs" name="outputs"
                    rows="3">${DOMUtils.escapeHtml(outputs)}</textarea>
          <small class="form-help">Datos de salida de la tarea. Se propagan automáticamente a las variables del proceso.</small>
        </div>
      </form>
    `;
  },

  // ===== EVENT LISTENERS DE MODALES =====

  setupCreateModalListeners() {
    const saveBtn = document.getElementById('save-task-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', async () => {
        await this.handleCreateSubmit();
      });
    }
  },

  setupEditModalListeners(id) {
    const updateBtn = document.getElementById('update-task-btn');
    if (updateBtn) {
      updateBtn.addEventListener('click', async () => {
        await this.handleEditSubmit(id);
      });
    }
  },

  async handleCreateSubmit() {
    const form = document.getElementById('create-task-form');
    if (!form || !DOMUtils.validateForm(form)) {
      return;
    }

    try {
      const formData = new FormData(form);
      const data = {
        processInstanceId: formData.get('processInstanceId'),
        name: formData.get('name'),
        description: formData.get('description') || null,
        formKey: formData.get('formKey'),
        order: parseInt(formData.get('order')),
        assignedTo: formData.get('assignedTo') || null,
        inputs: this.parseJsonField(formData.get('inputs'))
      };

      await API.tasks.create(data);

      ModalManager.hide();
      window.NotificationManager.success('Tarea creada exitosamente');
      await this.render(); // Recargar la lista

    } catch (error) {
      API.utils.handleError(error, 'creando tarea');
    }
  },

  async handleEditSubmit(id) {
    const form = document.getElementById('edit-task-form');
    if (!form || !DOMUtils.validateForm(form)) {
      return;
    }

    try {
      const formData = new FormData(form);
      const data = {
        name: formData.get('name'),
        description: formData.get('description') || null,
        formKey: formData.get('formKey'),
        order: parseInt(formData.get('order')),
        assignedTo: formData.get('assignedTo') || null,
        status: formData.get('status'),
        inputs: this.parseJsonField(formData.get('inputs')),
        outputs: this.parseJsonField(formData.get('outputs'))
      };

      await API.tasks.update(id, data);

      ModalManager.hide();
      window.NotificationManager.success('Tarea actualizada exitosamente');
      await this.render(); // Recargar la lista

    } catch (error) {
      API.utils.handleError(error, 'actualizando tarea');
    }
  },

  parseJsonField(jsonString) {
    if (!jsonString || !jsonString.trim()) {
      return null;
    }

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      throw new Error('El formato JSON no es válido');
    }
  }
};
