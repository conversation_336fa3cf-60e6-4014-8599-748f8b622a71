package co.com.gedsys.proezedure.repository;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.ProcessStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
@ActiveProfiles("test")
class ProcessInstanceRepositoryTest {

    @Autowired
    private ProcessInstanceRepository repository;

    @Test
    void shouldSaveAndFindProcessInstance() {
        // Given
        String variables = "{\"clienteId\": \"12345\", \"montoSolicitud\": 50000}";
        ProcessInstance instance = new ProcessInstance(
            "Instancia de Prueba", 
            "Descripción de la instancia", 
            variables
        );

        // When
        ProcessInstance saved = repository.save(instance);

        // Then
        assertThat(saved.getId()).isNotNull();
        assertThat(saved.getName()).isEqualTo("Instancia de Prueba");
        assertThat(saved.getVariables()).isEqualTo(variables);
        assertThat(saved.getStatus()).isEqualTo(ProcessStatus.RUNNING);
        assertThat(saved.getCreatedAt()).isNotNull();
        assertThat(saved.getUpdatedAt()).isNotNull();
    }

    @Test
    void shouldCreateInstanceFromTemplate() {
        // Given
        UUID templateId = UUID.randomUUID();
        String variables = "{\"estado\": \"iniciado\"}";
        ProcessInstance instance = new ProcessInstance(
            templateId,
            "Instancia desde Plantilla", 
            "Descripción", 
            variables
        );

        // When
        ProcessInstance saved = repository.save(instance);

        // Then
        assertThat(saved.getTemplateId()).isEqualTo(templateId);
        assertThat(saved.getName()).isEqualTo("Instancia desde Plantilla");
    }

    @Test
    void shouldFindAdHocInstances() {
        // Given
        ProcessInstance adHocInstance = new ProcessInstance("Ad-hoc", "Desc", "{}");
        ProcessInstance templateInstance = new ProcessInstance(UUID.randomUUID(), "Template", "Desc", "{}");
        
        repository.save(adHocInstance);
        repository.save(templateInstance);

        // When
        var adHocInstances = repository.findAdHocInstances();

        // Then
        assertThat(adHocInstances).hasSize(1);
        assertThat(adHocInstances.get(0).getName()).isEqualTo("Ad-hoc");
    }
}
