package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotNull;
import java.util.LinkedHashMap;

public class PatchVariablesRequest {

    @NotNull(message = "Patch variables are required")
    private LinkedHashMap<String, Object> patchVariables;

    private boolean merge = false;

    public PatchVariablesRequest() {
    }

    public PatchVariablesRequest(LinkedHashMap<String, Object> patchVariables) {
        this.patchVariables = patchVariables;
    }

    public PatchVariablesRequest(LinkedHashMap<String, Object> patchVariables, boolean merge) {
        this.patchVariables = patchVariables;
        this.merge = merge;
    }

    public LinkedHashMap<String, Object> getPatchVariables() {
        return patchVariables;
    }

    public void setPatchVariables(LinkedHashMap<String, Object> patchVariables) {
        this.patchVariables = patchVariables;
    }

    public boolean isMerge() {
        return merge;
    }

    public void setMerge(boolean merge) {
        this.merge = merge;
    }
}
