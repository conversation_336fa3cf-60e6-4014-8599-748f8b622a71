package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.ProcessStatus;
import co.com.gedsys.proezedure.dto.CreateProcessInstanceRequest;
import co.com.gedsys.proezedure.dto.ProcessInstanceResponse;
import co.com.gedsys.proezedure.service.ProcessInstanceService;
import co.com.gedsys.proezedure.util.JsonConverter;
import jakarta.validation.Valid;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/process-instances")
public class ProcessInstanceController {

    private final ProcessInstanceService service;
    private final JsonConverter jsonConverter;

    public ProcessInstanceController(ProcessInstanceService service, JsonConverter jsonConverter) {
        this.service = service;
        this.jsonConverter = jsonConverter;
    }

    @PostMapping
    public ResponseEntity<ProcessInstanceResponse> create(@Valid @RequestBody CreateProcessInstanceRequest request) {
        ProcessInstance instance;

        if (request.isFromTemplate()) {
            if (request.getDueDate() != null) {
                instance = service.createFromTemplate(
                    request.getTemplateId(),
                    request.getName(),
                    request.getDescription(),
                    request.getVariables(),
                    request.getDueDate()
                );
            } else {
                instance = service.createFromTemplate(
                    request.getTemplateId(),
                    request.getName(),
                    request.getDescription(),
                    request.getVariables()
                );
            }
        } else {
            if (request.getDueDate() != null) {
                instance = service.createAdHoc(
                    request.getName(),
                    request.getDescription(),
                    request.getVariables(),
                    request.getDueDate()
                );
            } else {
                instance = service.createAdHoc(
                    request.getName(),
                    request.getDescription(),
                    request.getVariables()
                );
            }
        }

        ProcessInstanceResponse response = ProcessInstanceResponse.from(instance, jsonConverter);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping
    public ResponseEntity<List<ProcessInstanceResponse>> findAll(
            @RequestParam(required = false) ProcessStatus status,
            @RequestParam(required = false) UUID templateId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        List<ProcessInstance> instances;
        
        if (status != null) {
            instances = service.findByStatus(status);
        } else if (templateId != null) {
            instances = service.findByTemplateId(templateId);
        } else if (startDate != null && endDate != null) {
            instances = service.findByCreatedAtBetween(startDate, endDate);
        } else {
            instances = service.findAll();
        }
        
        List<ProcessInstanceResponse> responses = instances.stream()
                .map(instance -> ProcessInstanceResponse.from(instance, jsonConverter))
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/ad-hoc")
    public ResponseEntity<List<ProcessInstanceResponse>> findAdHocInstances() {
        List<ProcessInstance> instances = service.findAdHocInstances();
        List<ProcessInstanceResponse> responses = instances.stream()
                .map(instance -> ProcessInstanceResponse.from(instance, jsonConverter))
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProcessInstanceResponse> findById(@PathVariable UUID id) {
        ProcessInstance instance = service.findById(id);
        ProcessInstanceResponse response = ProcessInstanceResponse.from(instance, jsonConverter);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProcessInstanceResponse> update(
            @PathVariable UUID id,
            @Valid @RequestBody CreateProcessInstanceRequest request) {

        ProcessInstance instance;
        if (request.getDueDate() != null) {
            instance = service.update(
                id,
                request.getName(),
                request.getDescription(),
                request.getVariables(),
                request.getDueDate()
            );
        } else {
            instance = service.update(
                id,
                request.getName(),
                request.getDescription(),
                request.getVariables()
            );
        }

        ProcessInstanceResponse response = ProcessInstanceResponse.from(instance, jsonConverter);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/variables")
    public ResponseEntity<ProcessInstanceResponse> updateVariables(
            @PathVariable UUID id,
            @RequestBody String variables) {
        
        ProcessInstance instance = service.updateVariables(id, variables);
        ProcessInstanceResponse response = ProcessInstanceResponse.from(instance, jsonConverter);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<ProcessInstanceResponse> updateStatus(
            @PathVariable UUID id,
            @RequestParam ProcessStatus status) {
        
        ProcessInstance instance = service.updateStatus(id, status);
        ProcessInstanceResponse response = ProcessInstanceResponse.from(instance, jsonConverter);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable UUID id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/count")
    public ResponseEntity<Long> countByStatus(@RequestParam ProcessStatus status) {
        long count = service.countByStatus(status);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/template/{templateId}/count")
    public ResponseEntity<Long> countByTemplateId(@PathVariable UUID templateId) {
        long count = service.countByTemplateId(templateId);
        return ResponseEntity.ok(count);
    }
}

// Endpoint adicional para crear instancias desde plantilla específica
@RestController
@RequestMapping("/api/process-definitions")
class ProcessDefinitionInstanceController {

    private final ProcessInstanceService processInstanceService;
    private final JsonConverter jsonConverter;

    public ProcessDefinitionInstanceController(ProcessInstanceService processInstanceService, JsonConverter jsonConverter) {
        this.processInstanceService = processInstanceService;
        this.jsonConverter = jsonConverter;
    }

    @PostMapping("/{templateId}/instances")
    public ResponseEntity<ProcessInstanceResponse> createInstanceFromTemplate(
            @PathVariable UUID templateId,
            @Valid @RequestBody CreateProcessInstanceRequest request) {

        ProcessInstance instance;
        if (request.getDueDate() != null) {
            instance = processInstanceService.createFromTemplate(
                templateId,
                request.getName(),
                request.getDescription(),
                request.getVariables(),
                request.getDueDate()
            );
        } else {
            instance = processInstanceService.createFromTemplate(
                templateId,
                request.getName(),
                request.getDescription(),
                request.getVariables()
            );
        }

        ProcessInstanceResponse response = ProcessInstanceResponse.from(instance, jsonConverter);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/{templateId}/instances")
    public ResponseEntity<List<ProcessInstanceResponse>> findInstancesByTemplate(@PathVariable UUID templateId) {
        List<ProcessInstance> instances = processInstanceService.findByTemplateId(templateId);
        List<ProcessInstanceResponse> responses = instances.stream()
                .map(instance -> ProcessInstanceResponse.from(instance, jsonConverter))
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }
}
