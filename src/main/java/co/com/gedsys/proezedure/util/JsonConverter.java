package co.com.gedsys.proezedure.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;

/**
 * Utilidad para convertir entre LinkedHashMap y String JSON
 * manteniendo el orden de las propiedades.
 */
@Component
public class JsonConverter {

    private final ObjectMapper objectMapper;

    public JsonConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Convierte un LinkedHashMap a String JSON.
     * 
     * @param map el mapa a convertir
     * @return String JSON o null si el mapa es null
     * @throws RuntimeException si hay error en la conversión
     */
    public String mapToJson(LinkedHashMap<String, Object> map) {
        if (map == null) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting map to JSON", e);
        }
    }

    /**
     * Convierte un String JSON a LinkedHashMap.
     * 
     * @param json el string JSON a convertir
     * @return LinkedHashMap o null si el string es null o vacío
     * @throws RuntimeException si hay error en la conversión
     */
    public LinkedHashMap<String, Object> jsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            TypeReference<LinkedHashMap<String, Object>> typeRef = new TypeReference<LinkedHashMap<String, Object>>() {};
            return objectMapper.readValue(json, typeRef);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON to map", e);
        }
    }

    /**
     * Convierte un String JSON a LinkedHashMap, retornando un mapa vacío si es null.
     * 
     * @param json el string JSON a convertir
     * @return LinkedHashMap (nunca null)
     * @throws RuntimeException si hay error en la conversión
     */
    public LinkedHashMap<String, Object> jsonToMapOrEmpty(String json) {
        LinkedHashMap<String, Object> result = jsonToMap(json);
        return result != null ? result : new LinkedHashMap<>();
    }

    /**
     * Convierte un LinkedHashMap a String JSON, retornando "{}" si es null.
     * 
     * @param map el mapa a convertir
     * @return String JSON (nunca null)
     * @throws RuntimeException si hay error en la conversión
     */
    public String mapToJsonOrEmpty(LinkedHashMap<String, Object> map) {
        if (map == null) {
            return "{}";
        }
        return mapToJson(map);
    }
}
