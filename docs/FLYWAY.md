# Flyway Database Migrations

Este documento describe cómo se utiliza Flyway para gestionar las migraciones de base de datos en el proyecto Proezedure.

## 📋 Tabla de Contenidos

- [Introducción](#introducción)
- [Configuración](#configuración)
- [Estructura de Migraciones](#estructura-de-migraciones)
- [Convenciones de Nomenclatura](#convenciones-de-nomenclatura)
- [Entornos](#entornos)
- [Comandos Útiles](#comandos-útiles)
- [Mejores Prácticas](#mejores-prácticas)
- [Troubleshooting](#troubleshooting)

## Introducción

Flyway es una herramienta de migración de base de datos que permite:

- **Versionado automático** del esquema de base de datos
- **Migraciones incrementales** y reproducibles
- **Historial completo** de cambios aplicados
- **Validación** de integridad de migraciones
- **Rollback** controlado (mediante nuevas migraciones)

## Configuración

### Dependencias Maven

```xml
<dependency>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-core</artifactId>
</dependency>
<dependency>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-database-postgresql</artifactId>
</dependency>
```

### Configuración por Entorno

#### Desarrollo (`application-dev.properties`)
```properties
spring.jpa.hibernate.ddl-auto=none
spring.flyway.enabled=true
spring.flyway.clean-disabled=false
spring.flyway.baseline-on-migrate=true
```

#### Producción (`application-prod.properties`)
```properties
spring.jpa.hibernate.ddl-auto=none
spring.flyway.enabled=true
spring.flyway.clean-disabled=true
spring.flyway.validate-on-migrate=true
spring.flyway.baseline-on-migrate=false
```

#### Testing (`application-test.properties`)
```properties
spring.jpa.hibernate.ddl-auto=create-drop
spring.flyway.enabled=false
```

## Estructura de Migraciones

```
src/main/resources/db/migration/
├── V1__Initial_schema.sql
├── V2__Add_due_date_to_process_instances.sql
└── V3__Add_new_feature.sql
```

## Convenciones de Nomenclatura

### Migraciones Versionadas
- **Formato**: `V{version}__{description}.sql`
- **Ejemplo**: `V1__Initial_schema.sql`
- **Versión**: Número entero incremental (1, 2, 3, ...)
- **Descripción**: Snake_case, descriptiva y concisa

### Migraciones Repetibles
- **Formato**: `R__{description}.sql`
- **Ejemplo**: `R__Update_views.sql`
- **Uso**: Para vistas, funciones, procedimientos almacenados

## Entornos

### Desarrollo
- Flyway habilitado con `clean` permitido
- Migraciones se ejecutan automáticamente
- Hibernate en modo `none` (sin DDL automático)

### Producción
- Flyway habilitado con `clean` deshabilitado
- Validación estricta de migraciones
- Baseline deshabilitado para mayor seguridad

### Testing
- Flyway deshabilitado
- Hibernate en modo `create-drop` para pruebas rápidas

## Comandos Útiles

### Maven Flyway Plugin

```bash
# Ver información de migraciones
./mvnw flyway:info

# Migrar a la última versión
./mvnw flyway:migrate

# Validar migraciones
./mvnw flyway:validate

# Limpiar base de datos (solo desarrollo)
./mvnw flyway:clean

# Reparar tabla de historial
./mvnw flyway:repair
```

### Configuración del Plugin

Agregar al `pom.xml` si se necesita configuración específica:

```xml
<plugin>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-maven-plugin</artifactId>
    <configuration>
        <url>*******************************************</url>
        <user>proezedure_user</user>
        <password>proezedure_pass</password>
    </configuration>
</plugin>
```

## Mejores Prácticas

### ✅ Hacer

1. **Nunca modificar migraciones aplicadas**
2. **Usar transacciones** cuando sea posible
3. **Probar migraciones** en entorno de desarrollo primero
4. **Hacer backup** antes de aplicar en producción
5. **Usar nombres descriptivos** para las migraciones
6. **Incluir comentarios** en SQL complejo
7. **Crear índices** después de insertar datos masivos

### ❌ No Hacer

1. **No modificar** migraciones ya aplicadas
2. **No usar DDL y DML** en la misma transacción (PostgreSQL)
3. **No crear migraciones** muy grandes
4. **No usar** `DROP TABLE` sin extrema precaución
5. **No depender** de datos específicos del entorno

### Ejemplo de Migración

```sql
-- V3__Add_user_management.sql
-- Agregar gestión de usuarios al sistema

-- Crear tabla de usuarios
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Crear índices
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);

-- Agregar columna de usuario asignado a tareas
ALTER TABLE tasks 
ADD COLUMN assigned_user_id UUID,
ADD CONSTRAINT fk_tasks_assigned_user 
    FOREIGN KEY (assigned_user_id) REFERENCES users(id);

-- Comentarios
COMMENT ON TABLE users IS 'Usuarios del sistema';
COMMENT ON COLUMN tasks.assigned_user_id IS 'Usuario asignado a la tarea';
```

## Troubleshooting

### Error: "Unsupported Database"
**Solución**: Agregar dependencia `flyway-database-postgresql`

### Error: "Migration checksum mismatch"
**Solución**: Usar `./mvnw flyway:repair` o crear nueva migración

### Error: "Schema not empty"
**Solución**: Usar `baseline-on-migrate=true` en desarrollo

### Migración Fallida
1. Revisar logs de Flyway
2. Verificar sintaxis SQL
3. Comprobar permisos de base de datos
4. Usar `flyway:repair` si es necesario

### Ver Estado de Migraciones

```bash
# Información detallada
./mvnw flyway:info

# Salida esperada:
# +-----------+---------+---------------------+------+---------------------+---------+
# | Category  | Version | Description         | Type | Installed On        | State   |
# +-----------+---------+---------------------+------+---------------------+---------+
# | Versioned | 1       | Initial schema      | SQL  | 2025-01-08 10:30:00 | Success |
# | Versioned | 2       | Add due date to ... | SQL  | 2025-01-08 10:30:01 | Success |
# +-----------+---------+---------------------+------+---------------------+---------+
```

## Recursos Adicionales

- [Documentación oficial de Flyway](https://flywaydb.org/documentation/)
- [Spring Boot Flyway Integration](https://docs.spring.io/spring-boot/docs/current/reference/html/howto.html#howto.data-initialization.migration-tool.flyway)
- [PostgreSQL Best Practices](https://wiki.postgresql.org/wiki/Don%27t_Do_This)
