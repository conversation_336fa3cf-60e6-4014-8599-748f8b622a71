package co.com.gedsys.proezedure.exception;

import java.util.UUID;

public class InvalidOrderSequenceException extends RuntimeException {
    
    public InvalidOrderSequenceException(Integer order, String entityType, UUID contextId) {
        super(String.format("Invalid order sequence %d for %s in context %s", order, entityType, contextId));
    }
    
    public InvalidOrderSequenceException(String message) {
        super(message);
    }
}
