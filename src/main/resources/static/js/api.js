/**
 * Capa de servicios para consumir la API REST
 */
window.API = {
  baseURL: '/api',
  
  /**
   * Configuración por defecto para fetch
   */
  defaultOptions: {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  },

  /**
   * Método genérico para hacer peticiones HTTP
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      ...this.defaultOptions,
      ...options,
      headers: {
        ...this.defaultOptions.headers,
        ...options.headers
      }
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Si la respuesta está vacía (204 No Content), retornar null
      if (response.status === 204) {
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  },

  /**
   * Métodos HTTP básicos
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  },

  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  },

  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  },

  // ===== PROCESS DEFINITIONS =====
  processDefinitions: {
    async getAll(params = {}) {
      return API.get('/process-definitions', params);
    },

    async getActive() {
      return API.get('/process-definitions/active');
    },

    async getById(id) {
      return API.get(`/process-definitions/${id}`);
    },

    async create(data) {
      return API.post('/process-definitions', data);
    },

    async update(id, data) {
      return API.put(`/process-definitions/${id}`, data);
    },

    async delete(id) {
      return API.delete(`/process-definitions/${id}`);
    },

    async updateStatus(id, status) {
      return API.request(`/process-definitions/${id}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ status })
      });
    }
  },

  // ===== PROCESS INSTANCES =====
  processInstances: {
    async getAll(params = {}) {
      return API.get('/process-instances', params);
    },

    async getById(id) {
      return API.get(`/process-instances/${id}`);
    },

    async create(data) {
      return API.post('/process-instances', data);
    },

    async createStandalone(data) {
      return API.post('/process-instances/standalone', data);
    },

    async update(id, data) {
      return API.put(`/process-instances/${id}`, data);
    },

    async delete(id) {
      return API.delete(`/process-instances/${id}`);
    },

    async getTasks(id) {
      return API.get(`/process-instances/${id}/tasks`);
    },

    async getNextTask(id) {
      return API.get(`/process-instances/${id}/tasks/next`);
    },

    async updateStatus(id, status) {
      return API.request(`/process-instances/${id}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ status })
      });
    }
  },

  // ===== TASKS =====
  tasks: {
    async getAll(params = {}) {
      return API.get('/tasks', params);
    },

    async getAvailable() {
      return API.get('/tasks/available');
    },

    async getById(id) {
      return API.get(`/tasks/${id}`);
    },

    async create(data) {
      return API.post('/tasks', data);
    },

    async update(id, data) {
      return API.put(`/tasks/${id}`, data);
    },

    async delete(id) {
      return API.delete(`/tasks/${id}`);
    },

    async assign(id, assignedTo) {
      return API.put(`/tasks/${id}/assign`, { assignedTo });
    },

    async claim(id, username) {
      return API.put(`/tasks/${id}/claim`, { username });
    },

    async complete(id, data = {}) {
      return API.put(`/tasks/${id}/complete`, data);
    },

    async getSubtasks(id) {
      return API.get(`/tasks/${id}/subtasks`);
    }
  },

  // ===== SUBTASKS =====
  subtasks: {
    async getAll(params = {}) {
      return API.get('/subtasks', params);
    },

    async getById(id) {
      return API.get(`/subtasks/${id}`);
    },

    async create(data) {
      return API.post('/subtasks', data);
    },

    async update(id, data) {
      return API.put(`/subtasks/${id}`, data);
    },

    async delete(id) {
      return API.delete(`/subtasks/${id}`);
    },

    async complete(id, data = {}) {
      return API.put(`/subtasks/${id}/complete`, data);
    }
  },

  // ===== USER TASKS =====
  userTasks: {
    async getByUser(username, params = {}) {
      return API.get(`/users/${username}/tasks`, params);
    },

    async getPending(username) {
      return API.get(`/users/${username}/tasks/pending`);
    },

    async getInProgress(username) {
      return API.get(`/users/${username}/tasks/in-progress`);
    },

    async getCompleted(username) {
      return API.get(`/users/${username}/tasks/completed`);
    }
  },

  // ===== VARIABLES =====
  variables: {
    async getByProcess(processId) {
      return API.get(`/process-instances/${processId}/variables`);
    },

    async updateByProcess(processId, variables) {
      return API.put(`/process-instances/${processId}/variables`, { variables });
    },

    async patchByProcess(processId, patchVariables, merge = false) {
      return API.request(`/process-instances/${processId}/variables`, {
        method: 'PATCH',
        body: JSON.stringify({ patchVariables, merge })
      });
    },

    async clearByProcess(processId) {
      return API.delete(`/process-instances/${processId}/variables`);
    },

    async getSpecificVariable(processId, variableName) {
      return API.get(`/process-instances/${processId}/variables/${variableName}`);
    },

    async setSpecificVariable(processId, variableName, variableValue) {
      return API.put(`/process-instances/${processId}/variables/${variableName}`, {
        variableName,
        variableValue
      });
    },

    async getByTask(taskId) {
      return API.get(`/tasks/${taskId}/variables`);
    },

    async updateByTask(taskId, variables) {
      return API.put(`/tasks/${taskId}/variables`, variables);
    }
  },

  // ===== UTILIDADES =====
  utils: {
    /**
     * Maneja errores de API de manera consistente
     */
    handleError(error, context = '') {
      console.error(`API Error ${context}:`, error);
      
      let message = 'Ha ocurrido un error inesperado';
      
      if (error.message) {
        if (error.message.includes('404')) {
          message = 'El recurso solicitado no fue encontrado';
        } else if (error.message.includes('400')) {
          message = 'Los datos enviados no son válidos';
        } else if (error.message.includes('500')) {
          message = 'Error interno del servidor';
        } else {
          message = error.message;
        }
      }
      
      // Mostrar notificación de error
      if (window.NotificationManager) {
        window.NotificationManager.error(message);
      } else {
        alert(message);
      }
      
      return message;
    },

    /**
     * Valida respuestas de API
     */
    validateResponse(response, expectedFields = []) {
      if (!response) {
        throw new Error('Respuesta vacía del servidor');
      }
      
      for (const field of expectedFields) {
        if (!(field in response)) {
          throw new Error(`Campo requerido '${field}' no encontrado en la respuesta`);
        }
      }
      
      return true;
    },

    /**
     * Construye parámetros de consulta de manera segura
     */
    buildQueryParams(params) {
      const filtered = Object.entries(params)
        .filter(([key, value]) => value !== null && value !== undefined && value !== '')
        .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
      
      return new URLSearchParams(filtered).toString();
    }
  }
};
