/* ===== VARIABLES CSS ===== */
:root {
  /* Paleta de colores monocromática */
  --color-white: #ffffff;
  --color-gray-50: #f8f9fa;
  --color-gray-100: #e9ecef;
  --color-gray-200: #dee2e6;
  --color-gray-300: #ced4da;
  --color-gray-400: #adb5bd;
  --color-gray-500: #6c757d;
  --color-gray-600: #495057;
  --color-gray-700: #343a40;
  --color-gray-800: #212529;
  --color-black: #000000;
  
  /* Colores semánticos */
  --color-primary: var(--color-gray-800);
  --color-secondary: var(--color-gray-600);
  --color-success: var(--color-gray-700);
  --color-warning: var(--color-gray-500);
  --color-danger: var(--color-gray-800);
  --color-info: var(--color-gray-600);
  
  /* Tipografía */
  --font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Espaciado */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  
  /* Bordes */
  --border-radius: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-width: 1px;
  
  /* Sombras */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Transiciones */
  --transition: all 0.2s ease-in-out;
}

/* ===== RESET Y BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-gray-800);
  background-color: var(--color-gray-50);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== LAYOUT PRINCIPAL ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.header {
  background-color: var(--color-white);
  border-bottom: var(--border-width) solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
}

.logo {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-primary);
}

.logo-link {
  text-decoration: none;
  color: inherit;
  transition: var(--transition);
}

.logo-link:hover {
  color: var(--color-gray-600);
}

.main {
  flex: 1;
  padding: var(--spacing-6) 0;
}

.footer {
  background-color: var(--color-white);
  border-top: var(--border-width) solid var(--color-gray-200);
  padding: var(--spacing-4) 0;
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

/* ===== NAVEGACIÓN ===== */
.nav-list {
  display: flex;
  list-style: none;
  gap: var(--spacing-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  text-decoration: none;
  color: var(--color-gray-600);
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
  background-color: var(--color-gray-100);
  color: var(--color-primary);
}

.nav-icon {
  font-size: var(--font-size-lg);
}

/* ===== BREADCRUMB ===== */
.breadcrumb {
  margin-bottom: var(--spacing-6);
}

.breadcrumb-list {
  display: flex;
  list-style: none;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin-left: var(--spacing-2);
  color: var(--color-gray-400);
}

.breadcrumb-item a {
  color: var(--color-gray-500);
  text-decoration: none;
  transition: var(--transition);
}

.breadcrumb-item a:hover {
  color: var(--color-primary);
}

/* ===== CONTENIDO ===== */
.content {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  padding: var(--spacing-6);
  min-height: 400px;
}

/* ===== LOADING ===== */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-12);
  color: var(--color-gray-500);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-gray-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== USUARIO ===== */
.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-gray-100);
  border-radius: var(--border-radius);
}

.user-name {
  font-weight: 500;
  color: var(--color-gray-700);
}

/* ===== UTILIDADES ===== */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-4 {
  margin-bottom: var(--spacing-4);
}

.mt-4 {
  margin-top: var(--spacing-4);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .nav-list {
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-link {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }

  .footer-content {
    flex-direction: column;
    gap: var(--spacing-2);
    text-align: center;
  }

  .content {
    padding: var(--spacing-4);
  }
}
