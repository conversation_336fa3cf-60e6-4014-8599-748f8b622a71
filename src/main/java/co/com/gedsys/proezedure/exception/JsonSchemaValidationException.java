package co.com.gedsys.proezedure.exception;

public class JsonSchemaValidationException extends RuntimeException {
    
    public JsonSchemaValidationException(String fieldName, String expectedSchema, String actualValue) {
        super(String.format("JSON schema validation failed for field '%s': expected %s, but got %s", 
              fieldName, expectedSchema, actualValue));
    }
    
    public JsonSchemaValidationException(String message) {
        super(message);
    }
    
    public JsonSchemaValidationException(String message, Throwable cause) {
        super(message, cause);
    }
}
