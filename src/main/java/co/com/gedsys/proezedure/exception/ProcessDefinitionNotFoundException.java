package co.com.gedsys.proezedure.exception;

import java.util.UUID;

public class ProcessDefinitionNotFoundException extends RuntimeException {
    
    public ProcessDefinitionNotFoundException(UUID id) {
        super("Process definition not found with id: " + id);
    }
    
    public ProcessDefinitionNotFoundException(String name, String version) {
        super("Process definition not found with name: " + name + " and version: " + version);
    }
}
