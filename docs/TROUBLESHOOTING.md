# Troubleshooting y Resolución de Problemas

## 📋 Índice

- [Problemas Comunes](#problemas-comunes)
- [Errores de Base de Datos](#errores-de-base-de-datos)
- [Errores de Validación](#errores-de-validación)
- [Problemas de JSON](#problemas-de-json)
- [Errores de Estado](#errores-de-estado)
- [Herramientas de Diagnóstico](#herramientas-de-diagnóstico)

## 🚨 Problemas Comunes

### 1. Error de Conexión a Base de Datos

**Síntoma**:
```
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
```

**Causas Posibles**:
- PostgreSQL no está ejecutándose
- Credenciales incorrectas
- Base de datos no existe
- Firewall bloqueando conexión

**Soluciones**:

1. **Verificar que PostgreSQL esté ejecutándose**:
```bash
# En macOS
brew services list | grep postgresql

# En Linux
sudo systemctl status postgresql

# En Windows
net start postgresql-x64-15
```

2. **Verificar credenciales**:
```bash
psql -h localhost -U proezedure_user -d proezedure
```

3. **Crear base de datos si no existe**:
```sql
CREATE DATABASE proezedure;
CREATE USER proezedure_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE proezedure TO proezedure_user;
```

4. **Verificar configuración**:
```yaml
# application.yml
spring:
  datasource:
    url: *******************************************
    username: proezedure_user
    password: your_password
```

### 2. Error de Compilación Maven

**Síntoma**:
```
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin
```

**Causas Posibles**:
- Versión incorrecta de Java
- Dependencias faltantes
- Código con errores de sintaxis

**Soluciones**:

1. **Verificar versión de Java**:
```bash
java -version
# Debe ser Java 21+
```

2. **Limpiar y recompilar**:
```bash
./mvnw clean compile
```

3. **Actualizar dependencias**:
```bash
./mvnw dependency:resolve
```

### 3. Tests Fallando

**Síntoma**:
```
[ERROR] Tests run: 96, Failures: 5, Errors: 2, Skipped: 0
```

**Causas Posibles**:
- Base de datos de test no configurada
- Mocks incorrectos
- Datos de test inconsistentes

**Soluciones**:

1. **Ejecutar tests individuales**:
```bash
./mvnw test -Dtest=TaskServiceTest
```

2. **Verificar configuración de test**:
```yaml
# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
```

3. **Limpiar datos entre tests**:
```java
@Transactional
@Rollback
class TaskServiceTest {
    // Tests aquí
}
```

## 🗄️ Errores de Base de Datos

### 1. Error de Migración de Schema

**Síntoma**:
```
org.hibernate.tool.schema.spi.SchemaManagementException: Unable to execute schema management to JDBC target
```

**Solución**:
```sql
-- Eliminar y recrear schema
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO proezedure_user;
```

### 2. Error de Tipo JSON

**Síntoma**:
```
org.postgresql.util.PSQLException: ERROR: column "variables" is of type json but expression is of type character varying
```

**Solución**:
```sql
-- Convertir columna a tipo JSON
ALTER TABLE process_instances 
ALTER COLUMN variables TYPE json USING variables::json;
```

### 3. Error de Constraint

**Síntoma**:
```
org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint
```

**Solución**:
```sql
-- Verificar datos duplicados
SELECT form_key, process_instance_id, COUNT(*) 
FROM tasks 
GROUP BY form_key, process_instance_id 
HAVING COUNT(*) > 1;

-- Eliminar duplicados manualmente
DELETE FROM tasks WHERE id IN (
    SELECT id FROM (
        SELECT id, ROW_NUMBER() OVER (
            PARTITION BY form_key, process_instance_id 
            ORDER BY created_at
        ) as rn FROM tasks
    ) t WHERE t.rn > 1
);
```

## ✅ Errores de Validación

### 1. FormKey Duplicado

**Error**:
```json
{
  "status": 409,
  "error": "Duplicate Form Key",
  "message": "Duplicate formKey 'review-docs' for Task in context 456e7890-e89b-12d3-a456-426614174000"
}
```

**Causa**: Intentar crear una tarea con un formKey que ya existe en el mismo proceso.

**Solución**:
```bash
# Verificar formKeys existentes
curl -X GET "http://localhost:8080/api/process-instances/456e7890-e89b-12d3-a456-426614174000/tasks"

# Usar formKey único
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "processInstanceId": "456e7890-e89b-12d3-a456-426614174000",
    "formKey": "review-docs-v2",
    ...
  }'
```

### 2. Orden Duplicado

**Error**:
```json
{
  "status": 400,
  "error": "Invalid Order Sequence",
  "message": "Invalid order sequence 1 for Task in context 456e7890-e89b-12d3-a456-426614174000"
}
```

**Solución**:
```bash
# Verificar órdenes existentes
curl -X GET "http://localhost:8080/api/process-instances/456e7890-e89b-12d3-a456-426614174000/tasks"

# Usar siguiente orden disponible
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "order": 3,
    ...
  }'
```

### 3. Usuario No Asignado

**Error**:
```json
{
  "status": 400,
  "error": "Task Not Assigned",
  "message": "Task 789e0123-e89b-12d3-a456-426614174000 is not assigned to user jmarin"
}
```

**Solución**:
```bash
# Verificar asignación actual
curl -X GET http://localhost:8080/api/tasks/789e0123-e89b-12d3-a456-426614174000

# Asignar tarea al usuario correcto
curl -X PATCH http://localhost:8080/api/tasks/789e0123-e89b-12d3-a456-426614174000/assign \
  -H "Content-Type: application/json" \
  -d '{"assignedTo": "jmarin"}'
```

## 📄 Problemas de JSON

### 1. JSON Malformado

**Error**:
```json
{
  "status": 400,
  "error": "JSON Schema Validation Failed",
  "message": "JSON schema validation failed for field 'inputs': expected valid JSON, but got {invalid}"
}
```

**Causa**: JSON con sintaxis incorrecta.

**Solución**:
```bash
# ❌ Incorrecto
{
  "inputs": "{invalid json}"
}

# ✅ Correcto
{
  "inputs": "{\"key\": \"value\"}"
}
```

### 2. JSON Vacío o Null

**Problema**: Variables no se inicializan correctamente.

**Solución**:
```bash
# Inicializar con objeto vacío
curl -X PUT http://localhost:8080/api/process-instances/456e7890-e29b-41d4-a716-446655440000/variables \
  -H "Content-Type: application/json" \
  -d '{
    "variables": "{}"
  }'
```

### 3. Error de Propagación de Variables

**Síntoma**: Variables no se actualizan después de completar tarea.

**Diagnóstico**:
```bash
# Verificar outputs de la tarea
curl -X GET http://localhost:8080/api/tasks/789e0123-e89b-12d3-a456-426614174000

# Verificar variables del proceso
curl -X GET http://localhost:8080/api/process-instances/456e7890-e29b-41d4-a716-446655440000/variables
```

**Solución**:
```bash
# Propagar manualmente si es necesario
curl -X POST http://localhost:8080/api/process-instances/456e7890-e29b-41d4-a716-446655440000/variables/propagate \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "789e0123-e89b-12d3-a456-426614174000",
    "taskOutputs": "{\"resultado\": \"aprobado\"}",
    "outputFields": ["resultado"]
  }'
```

## 🔄 Errores de Estado

### 1. Transición de Estado Inválida

**Error**:
```json
{
  "status": 400,
  "error": "Invalid State Transition",
  "message": "Invalid state transition for Task: cannot change from COMPLETED to IN_PROGRESS"
}
```

**Causa**: Intentar cambiar una tarea completada a estado en progreso.

**Estados Válidos para Tasks**:
- CREATED → ASSIGNED, IN_PROGRESS, CANCELLED
- ASSIGNED → IN_PROGRESS, CANCELLED  
- IN_PROGRESS → COMPLETED, CANCELLED
- COMPLETED/CANCELLED → Terminal (no cambios)

**Solución**: Verificar estado actual antes de intentar cambios.

### 2. Subtareas Incompletas

**Error**:
```json
{
  "status": 400,
  "error": "Task Cannot Be Completed",
  "message": "Task 789e0123-e89b-12d3-a456-426614174000 cannot be completed because not all subtasks are completed"
}
```

**Solución**:
```bash
# Verificar estado de subtareas
curl -X GET http://localhost:8080/api/tasks/789e0123-e89b-12d3-a456-426614174000/subtasks

# Completar subtareas pendientes
curl -X PATCH http://localhost:8080/api/subtasks/{subtask-id}/complete \
  -H "Content-Type: application/json" \
  -d '{"outputs": "{\"resultado\": \"completado\"}"}'
```

## 🔧 Herramientas de Diagnóstico

### 1. Health Check

```bash
# Verificar estado de la aplicación
curl -X GET http://localhost:8080/actuator/health
```

**Respuesta esperada**:
```json
{
  "status": "UP",
  "components": {
    "db": {
      "status": "UP",
      "details": {
        "database": "PostgreSQL",
        "validationQuery": "isValid()"
      }
    }
  }
}
```

### 2. Logs de Aplicación

**Configurar logging detallado**:
```yaml
# application-dev.yml
logging:
  level:
    co.com.gedsys.proezedure: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
```

**Verificar logs**:
```bash
# Seguir logs en tiempo real
tail -f logs/application.log

# Buscar errores específicos
grep "ERROR" logs/application.log
```

### 3. Base de Datos

**Conectar a PostgreSQL**:
```bash
psql -h localhost -U proezedure_user -d proezedure
```

**Consultas útiles**:
```sql
-- Verificar tablas
\dt

-- Contar registros
SELECT 
  'process_definitions' as table_name, COUNT(*) as count FROM process_definitions
UNION ALL
SELECT 'process_instances', COUNT(*) FROM process_instances
UNION ALL  
SELECT 'tasks', COUNT(*) FROM tasks
UNION ALL
SELECT 'subtasks', COUNT(*) FROM subtasks;

-- Verificar integridad referencial
SELECT t.id, t.process_instance_id, pi.id as pi_exists
FROM tasks t
LEFT JOIN process_instances pi ON t.process_instance_id = pi.id
WHERE pi.id IS NULL;
```

### 4. Testing de API

**Usar curl para testing rápido**:
```bash
# Script de verificación básica
#!/bin/bash

echo "Testing API endpoints..."

# Health check
echo "1. Health check:"
curl -s http://localhost:8080/actuator/health | jq .

# List process definitions
echo "2. Process definitions:"
curl -s http://localhost:8080/api/process-definitions | jq length

# List process instances  
echo "3. Process instances:"
curl -s http://localhost:8080/api/process-instances | jq length

echo "API testing completed."
```

### 5. Monitoreo de Performance

**Métricas de JVM**:
```bash
curl -s http://localhost:8080/actuator/metrics/jvm.memory.used | jq .
```

**Métricas de base de datos**:
```bash
curl -s http://localhost:8080/actuator/metrics/hikaricp.connections.active | jq .
```

## 📞 Soporte

### Información para Reportar Bugs

Cuando reportes un problema, incluye:

1. **Versión del sistema**
2. **Pasos para reproducir**
3. **Mensaje de error completo**
4. **Logs relevantes**
5. **Configuración de entorno**

### Logs Importantes

```bash
# Exportar logs para soporte
tar -czf logs-$(date +%Y%m%d).tar.gz logs/
```

### Contacto

- **Equipo de Desarrollo**: Sucomunicación - GEDSYS2
- **Documentación**: [docs/](docs/)
- **Issues**: GitHub Issues (si aplica)
