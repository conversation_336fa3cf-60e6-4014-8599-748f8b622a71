/**
 * Componente Dashboard - Vista principal del sistema
 */
window.DashboardComponent = {
  name: 'dashboard',
  
  async render() {
    try {
      // Mostrar loading
      DOMUtils.show('loading');
      
      // Obtener datos del dashboard
      const [
        processDefinitions,
        processInstances,
        availableTasks,
        recentInstances
      ] = await Promise.all([
        API.processDefinitions.getActive(),
        API.processInstances.getAll({ limit: 10 }),
        API.tasks.getAvailable(),
        API.processInstances.getAll({ limit: 5, sort: 'createdAt', order: 'desc' })
      ]);

      // Ocultar loading
      DOMUtils.hide('loading');

      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Dashboard' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate({
        processDefinitions,
        processInstances,
        availableTasks,
        recentInstances
      });

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando dashboard');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate(data) {
    const {
      processDefinitions = [],
      processInstances = [],
      availableTasks = [],
      recentInstances = []
    } = data;

    return `
      <div class="dashboard">
        <div class="dashboard-header mb-4">
          <h1>Dashboard</h1>
          <p class="text-gray-600">Resumen general del sistema de gestión de procesos</p>
        </div>

        <!-- Métricas principales -->
        <div class="grid grid-cols-4 mb-6">
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value">${processDefinitions.length}</div>
              <div class="metric-label">Plantillas Activas</div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value">${processInstances.length}</div>
              <div class="metric-label">Procesos Activos</div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value">${availableTasks.length}</div>
              <div class="metric-label">Tareas Disponibles</div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value">${this.getCompletedTasksCount(processInstances)}</div>
              <div class="metric-label">Tareas Completadas</div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2">
          <!-- Procesos recientes -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Procesos Recientes</h3>
            </div>
            <div class="card-body">
              ${recentInstances.length > 0 ? this.getRecentProcessesTemplate(recentInstances) : '<p class="text-gray-500">No hay procesos recientes</p>'}
            </div>
            <div class="card-footer">
              <a href="#/process-instances" class="btn btn-outline btn-sm">Ver todos los procesos</a>
            </div>
          </div>

          <!-- Tareas disponibles -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Tareas Disponibles</h3>
            </div>
            <div class="card-body">
              ${availableTasks.length > 0 ? this.getAvailableTasksTemplate(availableTasks.slice(0, 5)) : '<p class="text-gray-500">No hay tareas disponibles</p>'}
            </div>
            <div class="card-footer">
              <a href="#/tasks" class="btn btn-outline btn-sm">Ver todas las tareas</a>
            </div>
          </div>
        </div>

        <!-- Acciones rápidas -->
        <div class="card mt-6">
          <div class="card-header">
            <h3 class="card-title">Acciones Rápidas</h3>
          </div>
          <div class="card-body">
            <div class="grid grid-cols-3">
              <button class="btn btn-primary" data-action="create-process">
                <span>📋</span>
                Crear Nuevo Proceso
              </button>
              
              <button class="btn btn-secondary" data-action="create-definition">
                <span>⚙️</span>
                Nueva Plantilla
              </button>
              
              <button class="btn btn-outline" data-action="view-my-tasks">
                <span>👤</span>
                Mis Tareas
              </button>
            </div>
          </div>
        </div>
      </div>

      <style>
        .metric-value {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .metric-label {
          font-size: 0.875rem;
          color: var(--color-gray-600);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .dashboard-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .process-item, .task-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem 0;
          border-bottom: 1px solid var(--color-gray-200);
        }
        
        .process-item:last-child, .task-item:last-child {
          border-bottom: none;
        }
        
        .process-name, .task-name {
          font-weight: 500;
          color: var(--color-gray-800);
        }
        
        .process-meta, .task-meta {
          font-size: 0.875rem;
          color: var(--color-gray-500);
        }
      </style>
    `;
  },

  getRecentProcessesTemplate(processes) {
    return processes.map(process => `
      <div class="process-item">
        <div>
          <div class="process-name">${DOMUtils.escapeHtml(process.name)}</div>
          <div class="process-meta">
            ${Formatters.formatStatus(process.status)} • 
            ${Formatters.formatRelativeTime(process.createdAt)}
          </div>
        </div>
        <a href="#/process-instances/${process.id}" class="btn btn-sm btn-outline">Ver</a>
      </div>
    `).join('');
  },

  getAvailableTasksTemplate(tasks) {
    return tasks.map(task => `
      <div class="task-item">
        <div>
          <div class="task-name">${DOMUtils.escapeHtml(task.name)}</div>
          <div class="task-meta">
            ${Formatters.formatStatus(task.status)} • 
            ${task.assignedTo ? `Asignada a ${task.assignedTo}` : 'Sin asignar'}
          </div>
        </div>
        <a href="#/tasks/${task.id}" class="btn btn-sm btn-outline">Ver</a>
      </div>
    `).join('');
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar el dashboard</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener los datos del sistema.</p>
          <button class="btn btn-primary" onclick="window.Router.navigate('/')">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  getCompletedTasksCount(processes) {
    // Simulación del conteo de tareas completadas
    // En una implementación real, esto vendría de la API
    return processes.filter(p => p.status === 'COMPLETED').length * 3;
  },

  setupEventListeners() {
    // Acciones rápidas
    const actionButtons = document.querySelectorAll('[data-action]');
    actionButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = e.target.closest('[data-action]').dataset.action;
        this.handleQuickAction(action);
      });
    });
  },

  handleQuickAction(action) {
    switch (action) {
      case 'create-process':
        if (window.Router) {
          window.Router.navigate('/process-instances');
        } else {
          window.location.hash = '#/process-instances';
        }
        break;
      case 'create-definition':
        if (window.Router) {
          window.Router.navigate('/process-definitions');
        } else {
          window.location.hash = '#/process-definitions';
        }
        break;
      case 'view-my-tasks':
        if (window.Router) {
          window.Router.navigate('/my-tasks');
        } else {
          window.location.hash = '#/my-tasks';
        }
        break;
      default:
        console.warn('Acción no reconocida:', action);
    }
  }
};
