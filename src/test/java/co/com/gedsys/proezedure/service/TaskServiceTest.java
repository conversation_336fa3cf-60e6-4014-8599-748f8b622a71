package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;
import co.com.gedsys.proezedure.exception.TaskCannotBeCompletedException;
import co.com.gedsys.proezedure.exception.TaskNotAssignedException;
import co.com.gedsys.proezedure.exception.TaskNotFoundException;
import co.com.gedsys.proezedure.repository.SubtaskRepository;
import co.com.gedsys.proezedure.repository.TaskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskServiceTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private SubtaskRepository subtaskRepository;

    @Mock
    private VariableService variableService;

    @Mock
    private ValidationService validationService;

    @InjectMocks
    private TaskService taskService;

    private Task testTask;
    private UUID taskId;
    private UUID processInstanceId;

    @BeforeEach
    void setUp() {
        taskId = UUID.randomUUID();
        processInstanceId = UUID.randomUUID();
        
        testTask = new Task(processInstanceId, "Test Task", "Description", "form-key-1", 1, "{}");
        testTask.setId(taskId);
    }

    @Test
    void shouldCreateTask() {
        // Given
        when(taskRepository.save(any(Task.class))).thenReturn(testTask);

        // When
        Task result = taskService.create(processInstanceId, "Test Task", "Description", "form-key-1", 1, "{}");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Task");
        assertThat(result.getProcessInstanceId()).isEqualTo(processInstanceId);
        verify(taskRepository).save(any(Task.class));
    }

    @Test
    void shouldFindTaskById() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // When
        Task result = taskService.findById(taskId);

        // Then
        assertThat(result).isEqualTo(testTask);
        verify(taskRepository).findById(taskId);
    }

    @Test
    void shouldThrowExceptionWhenTaskNotFound() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> taskService.findById(taskId))
                .isInstanceOf(TaskNotFoundException.class)
                .hasMessageContaining(taskId.toString());

        verify(taskRepository).findById(taskId);
    }

    @Test
    void shouldAssignTask() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(testTask)).thenReturn(testTask);

        // When
        Task result = taskService.assign(taskId, "jmarin");

        // Then
        assertThat(result.getAssignedTo()).isEqualTo("jmarin");
        verify(taskRepository).findById(taskId);
        verify(taskRepository).save(testTask);
    }

    @Test
    void shouldClaimTask() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(testTask)).thenReturn(testTask);

        // When
        Task result = taskService.claim(taskId, "jmarin");

        // Then
        assertThat(result.getAssignedTo()).isEqualTo("jmarin");
        assertThat(result.getStatus()).isEqualTo(TaskStatus.IN_PROGRESS);
        assertThat(result.getClaimedAt()).isNotNull();
        verify(taskRepository).findById(taskId);
        verify(taskRepository).save(testTask);
    }

    @Test
    void shouldThrowExceptionWhenClaimingTaskAssignedToOtherUser() {
        // Given
        testTask.setAssignedTo("otheruser");
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // When & Then
        assertThatThrownBy(() -> taskService.claim(taskId, "jmarin"))
                .isInstanceOf(TaskNotAssignedException.class)
                .hasMessageContaining("jmarin");

        verify(taskRepository).findById(taskId);
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldCompleteTask() {
        // Given
        testTask.setAssignedTo("jmarin");
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(testTask)).thenReturn(testTask);

        // When
        Task result = taskService.complete(taskId, "jmarin", "{\"result\": \"success\"}");

        // Then
        assertThat(result.getStatus()).isEqualTo(TaskStatus.COMPLETED);
        assertThat(result.getOutputs()).isEqualTo("{\"result\": \"success\"}");
        assertThat(result.getCompletedAt()).isNotNull();
        verify(taskRepository).findById(taskId);
        verify(taskRepository).save(testTask);
    }

    @Test
    void shouldThrowExceptionWhenCompletingTaskNotAssignedToUser() {
        // Given
        testTask.setAssignedTo("otheruser");
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        doThrow(new TaskNotAssignedException(taskId, "jmarin"))
                .when(validationService).validateUserPermission(testTask, "jmarin");

        // When & Then
        assertThatThrownBy(() -> taskService.complete(taskId, "jmarin", "{}"))
                .isInstanceOf(TaskNotAssignedException.class)
                .hasMessageContaining("jmarin");

        verify(taskRepository).findById(taskId);
        verify(validationService).validateUserPermission(testTask, "jmarin");
        verify(taskRepository, never()).save(any());
    }

    @Test
    void shouldUpdateTaskStatus() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(testTask)).thenReturn(testTask);

        // When
        Task result = taskService.updateStatus(taskId, TaskStatus.CANCELLED);

        // Then
        assertThat(result.getStatus()).isEqualTo(TaskStatus.CANCELLED);
        verify(taskRepository).findById(taskId);
        verify(taskRepository).save(testTask);
    }

    @Test
    void shouldDeleteTask() {
        // Given
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // When
        taskService.delete(taskId);

        // Then
        verify(taskRepository).findById(taskId);
        verify(taskRepository).delete(testTask);
    }
}
