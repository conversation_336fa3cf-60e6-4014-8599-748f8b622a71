package co.com.gedsys.proezedure.repository;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.TemplateStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
@ActiveProfiles("test")
class ProcessDefinitionRepositoryTest {

    @Autowired
    private ProcessDefinitionRepository repository;

    @Test
    void shouldSaveAndFindProcessDefinition() {
        // Given
        String defaultVariables = "{\"clienteId\": null, \"estado\": \"inicial\"}";
        ProcessDefinition definition = new ProcessDefinition(
            "Proceso de Prueba", 
            "Descripción del proceso", 
            "1.0", 
            defaultVariables
        );

        // When
        ProcessDefinition saved = repository.save(definition);

        // Then
        assertThat(saved.getId()).isNotNull();
        assertThat(saved.getName()).isEqualTo("Proceso de Prueba");
        assertThat(saved.getDefaultVariables()).isEqualTo(defaultVariables);
        assertThat(saved.getStatus()).isEqualTo(TemplateStatus.ACTIVE);
        assertThat(saved.getCreatedAt()).isNotNull();
    }

    @Test
    void shouldFindByStatus() {
        // Given
        ProcessDefinition definition = new ProcessDefinition("Test", "Desc", "1.0", "{}");
        repository.save(definition);

        // When
        var activeDefinitions = repository.findByStatus(TemplateStatus.ACTIVE);

        // Then
        assertThat(activeDefinitions).hasSize(1);
        assertThat(activeDefinitions.get(0).getName()).isEqualTo("Test");
    }
}
