package co.com.gedsys.proezedure.exception;

import java.util.UUID;

public class DuplicateFormKeyException extends RuntimeException {
    
    public DuplicateFormKeyException(String formKey, String entityType, UUID contextId) {
        super(String.format("Duplicate formKey '%s' for %s in context %s", formKey, entityType, contextId));
    }
    
    public DuplicateFormKeyException(String formKey, String entityType) {
        super(String.format("Duplicate formKey '%s' for %s", formKey, entityType));
    }
}
