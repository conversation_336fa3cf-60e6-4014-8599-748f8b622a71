package co.com.gedsys.proezedure.dto;

import co.com.gedsys.proezedure.domain.Subtask;
import co.com.gedsys.proezedure.domain.SubtaskStatus;

import java.time.LocalDateTime;
import java.util.UUID;

public class SubtaskResponse {

    private UUID id;
    private UUID taskId;
    private String name;
    private String description;
    private String formKey;
    private Integer order;
    private SubtaskStatus status;
    private String inputs;
    private String outputs;
    private LocalDateTime createdAt;
    private LocalDateTime completedAt;
    private LocalDateTime updatedAt;

    public SubtaskResponse() {
    }

    public SubtaskResponse(Subtask subtask) {
        this.id = subtask.getId();
        this.taskId = subtask.getTaskId();
        this.name = subtask.getName();
        this.description = subtask.getDescription();
        this.formKey = subtask.getFormKey();
        this.order = subtask.getOrder();
        this.status = subtask.getStatus();
        this.inputs = subtask.getInputs();
        this.outputs = subtask.getOutputs();
        this.createdAt = subtask.getCreatedAt();
        this.completedAt = subtask.getCompletedAt();
        this.updatedAt = subtask.getUpdatedAt();
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getTaskId() {
        return taskId;
    }

    public void setTaskId(UUID taskId) {
        this.taskId = taskId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public SubtaskStatus getStatus() {
        return status;
    }

    public void setStatus(SubtaskStatus status) {
        this.status = status;
    }

    public String getInputs() {
        return inputs;
    }

    public void setInputs(String inputs) {
        this.inputs = inputs;
    }

    public String getOutputs() {
        return outputs;
    }

    public void setOutputs(String outputs) {
        this.outputs = outputs;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isCompleted() {
        return status == SubtaskStatus.COMPLETED;
    }
}
