# Casos de Uso y Ejemplos Prácticos

## 📋 Índice

- [Casos de Uso Principales](#casos-de-uso-principales)
- [Flujos de Trabajo Completos](#flujos-de-trabajo-completos)
- [Ejemplos de JSON](#ejemplos-de-json)
- [Escenarios Reales](#escenarios-reales)
- [Patrones Comunes](#patrones-comunes)

## 🎯 Casos de Uso Principales

### 1. Proceso de Aprobación de Crédito

**Descripción**: Proceso completo para aprobar solicitudes de crédito con múltiples etapas de validación.

**Actores**: Cliente, Analista de Crédito, Supervisor, Sistema

**Flujo Principal**:
1. Cliente envía solicitud
2. Sistema crea instancia de proceso
3. Analista revisa documentos
4. Sistema valida información
5. Supervisor aprueba/rechaza
6. Sistema notifica resultado

### 2. Gestión de Solicitudes de Vacaciones

**Descripción**: Proceso para gestionar solicitudes de vacaciones de empleados.

**Actores**: Empleado, Jefe Directo, RRHH

**Flujo Principal**:
1. Empleado solicita vacaciones
2. Jefe directo revisa y aprueba
3. RRHH valida disponibilidad
4. Sistema actualiza calendario

### 3. Proceso de Onboarding

**Descripción**: Proceso de incorporación de nuevos empleados.

**Actores**: Nuevo Empleado, RRHH, IT, Supervisor

**Flujo Principal**:
1. RRHH crea proceso de onboarding
2. IT configura accesos
3. Supervisor asigna tareas iniciales
4. Empleado completa formación

## 🔄 Flujos de Trabajo Completos

### Flujo 1: Proceso de Crédito Completo

#### Paso 1: Crear Plantilla de Proceso
```bash
curl -X POST http://localhost:8080/api/process-definitions \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Proceso de Crédito Personal",
    "description": "Proceso completo para aprobación de créditos personales",
    "version": "2.0",
    "defaultVariables": "{
      \"cliente\": {
        \"id\": null,
        \"nombre\": null,
        \"cedula\": null,
        \"ingresos\": 0
      },
      \"solicitud\": {
        \"monto\": 0,
        \"plazo\": 0,
        \"proposito\": null,
        \"fecha\": null
      },
      \"evaluacion\": {
        \"puntaje\": 0,
        \"riesgo\": \"BAJO\",
        \"observaciones\": []
      },
      \"decision\": {
        \"aprobado\": false,
        \"montoAprobado\": 0,
        \"tasaInteres\": 0,
        \"fechaDecision\": null,
        \"aprobadoPor\": null
      }
    }"
  }'
```

#### Paso 2: Crear Instancia para Cliente Específico
```bash
curl -X POST http://localhost:8080/api/process-instances \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": "550e8400-e29b-41d4-a716-446655440000",
    "name": "Crédito Personal - Juan Pérez",
    "description": "Solicitud de crédito personal por $50,000",
    "variables": "{
      \"cliente\": {
        \"id\": \"12345\",
        \"nombre\": \"Juan Pérez\",
        \"cedula\": \"1234567890\",
        \"ingresos\": 3000000
      },
      \"solicitud\": {
        \"monto\": 50000000,
        \"plazo\": 36,
        \"proposito\": \"Vivienda\",
        \"fecha\": \"2024-01-15\"
      }
    }"
  }'
```

#### Paso 3: Crear Tareas Secuenciales

**Tarea 1: Verificación de Documentos**
```bash
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "processInstanceId": "660e8400-e29b-41d4-a716-446655440000",
    "name": "Verificar Documentos",
    "description": "Verificar autenticidad y completitud de documentos",
    "formKey": "verify-documents",
    "order": 1,
    "inputs": "{
      \"documentosRequeridos\": [
        \"cedula\",
        \"certificadoIngresos\",
        \"extractoBancario\",
        \"referenciaComercial\"
      ],
      \"criteriosValidacion\": {
        \"vigencia\": 90,
        \"autenticidad\": true
      }
    }"
  }'
```

**Tarea 2: Análisis de Riesgo**
```bash
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "processInstanceId": "660e8400-e29b-41d4-a716-446655440000",
    "name": "Análisis de Riesgo",
    "description": "Evaluar riesgo crediticio del solicitante",
    "formKey": "risk-analysis",
    "order": 2,
    "inputs": "{
      \"factoresEvaluacion\": [
        \"historialCrediticio\",
        \"capacidadPago\",
        \"estabilidadLaboral\",
        \"patrimonioNeto\"
      ],
      \"modeloRiesgo\": \"SCORING_V2\"
    }"
  }'
```

#### Paso 4: Ejecutar Tareas con Subtareas

**Crear Subtareas para Verificación**
```bash
# Subtarea: Verificar Cédula
curl -X POST http://localhost:8080/api/subtasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "770e8400-e29b-41d4-a716-446655440000",
    "name": "Verificar Cédula",
    "description": "Validar autenticidad de cédula de ciudadanía",
    "formKey": "verify-id",
    "order": 1,
    "inputs": "{
      \"numeroDocumento\": \"1234567890\",
      \"tipoValidacion\": \"REGISTRADURIA\"
    }"
  }'

# Subtarea: Verificar Ingresos
curl -X POST http://localhost:8080/api/subtasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "770e8400-e29b-41d4-a716-446655440000",
    "name": "Verificar Ingresos",
    "description": "Validar certificado de ingresos",
    "formKey": "verify-income",
    "order": 2,
    "inputs": "{
      \"ingresosDeclardos\": 3000000,
      \"fuenteValidacion\": \"DIAN\"
    }"
  }'
```

#### Paso 5: Completar Subtareas
```bash
# Completar verificación de cédula
curl -X PATCH http://localhost:8080/api/subtasks/880e8400-e29b-41d4-a716-446655440000/complete \
  -H "Content-Type: application/json" \
  -d '{
    "outputs": "{
      \"documentoValido\": true,
      \"fechaExpedicion\": \"2020-03-15\",
      \"lugarExpedicion\": \"Bogotá\",
      \"observaciones\": \"Documento en perfecto estado\"
    }"
  }'

# Completar verificación de ingresos
curl -X PATCH http://localhost:8080/api/subtasks/990e8400-e29b-41d4-a716-446655440000/complete \
  -H "Content-Type: application/json" \
  -d '{
    "outputs": "{
      \"ingresosVerificados\": 3200000,
      \"diferenciaDeclardos\": 200000,
      \"fuenteConfiable\": true,
      \"observaciones\": \"Ingresos superiores a los declarados\"
    }"
  }'
```

#### Paso 6: Completar Tarea Principal
```bash
curl -X PATCH http://localhost:8080/api/tasks/770e8400-e29b-41d4-a716-446655440000/assign \
  -H "Content-Type: application/json" \
  -d '{"assignedTo": "analista.credito"}'

curl -X PATCH http://localhost:8080/api/tasks/770e8400-e29b-41d4-a716-446655440000/complete \
  -H "Content-Type: application/json" \
  -d '{
    "username": "analista.credito",
    "outputs": "{
      \"documentosCompletos\": true,
      \"documentosValidos\": true,
      \"observacionesGenerales\": \"Toda la documentación está en orden\",
      \"recomendacion\": \"CONTINUAR_PROCESO\"
    }"
  }'
```

#### Paso 7: Verificar Propagación de Variables
```bash
curl -X GET http://localhost:8080/api/process-instances/660e8400-e29b-41d4-a716-446655440000/variables
```

**Respuesta esperada:**
```json
{
  "processInstanceId": "660e8400-e29b-41d4-a716-446655440000",
  "variables": "{
    \"cliente\": {
      \"id\": \"12345\",
      \"nombre\": \"Juan Pérez\",
      \"cedula\": \"1234567890\",
      \"ingresos\": 3000000
    },
    \"solicitud\": {
      \"monto\": 50000000,
      \"plazo\": 36,
      \"proposito\": \"Vivienda\",
      \"fecha\": \"2024-01-15\"
    },
    \"documentosCompletos\": true,
    \"documentosValidos\": true,
    \"observacionesGenerales\": \"Toda la documentación está en orden\",
    \"recomendacion\": \"CONTINUAR_PROCESO\"
  }",
  "lastUpdated": "2024-01-15T14:30:00Z"
}
```

## 📊 Ejemplos de JSON

### Variables de Proceso Complejas

#### Proceso de Contratación
```json
{
  "candidato": {
    "id": "CAND-2024-001",
    "datosPersonales": {
      "nombre": "María",
      "apellido": "González",
      "email": "<EMAIL>",
      "telefono": "+57 ************"
    },
    "experiencia": [
      {
        "empresa": "TechCorp",
        "cargo": "Desarrolladora Senior",
        "duracion": "3 años",
        "tecnologias": ["Java", "Spring", "PostgreSQL"]
      }
    ],
    "educacion": {
      "titulo": "Ingeniería de Sistemas",
      "universidad": "Universidad Nacional",
      "año": 2018
    }
  },
  "posicion": {
    "codigo": "DEV-BACKEND-001",
    "titulo": "Desarrollador Backend Senior",
    "departamento": "Tecnología",
    "salarioOfrecido": 8000000,
    "modalidad": "Remoto"
  },
  "evaluacion": {
    "entrevistas": [
      {
        "tipo": "Técnica",
        "entrevistador": "Juan Pérez",
        "fecha": "2024-01-10",
        "puntaje": 85,
        "observaciones": "Excelente conocimiento técnico"
      },
      {
        "tipo": "Cultural",
        "entrevistador": "Ana López",
        "fecha": "2024-01-12",
        "puntaje": 90,
        "observaciones": "Muy buen fit cultural"
      }
    ],
    "pruebasTecnicas": {
      "algoritmos": 88,
      "diseñoSistemas": 82,
      "codingChallenge": 90
    },
    "referencias": [
      {
        "nombre": "Carlos Ruiz",
        "empresa": "TechCorp",
        "recomendacion": "Altamente recomendada"
      }
    ]
  },
  "decision": {
    "aprobado": true,
    "salarioFinal": 8500000,
    "fechaInicio": "2024-02-01",
    "observaciones": "Candidata excepcional"
  }
}
```

#### Proceso de Compras
```json
{
  "solicitud": {
    "numero": "SOL-2024-0156",
    "solicitante": {
      "empleado": "Pedro Martínez",
      "departamento": "IT",
      "centroCosto": "CC-IT-001"
    },
    "fecha": "2024-01-15",
    "urgencia": "MEDIA"
  },
  "items": [
    {
      "codigo": "LAPTOP-001",
      "descripcion": "Laptop Dell Latitude 5520",
      "cantidad": 2,
      "precioUnitario": 3500000,
      "proveedor": "Dell Colombia",
      "justificacion": "Reemplazo de equipos obsoletos"
    },
    {
      "codigo": "MONITOR-001",
      "descripcion": "Monitor 24 pulgadas",
      "cantidad": 2,
      "precioUnitario": 800000,
      "proveedor": "LG Electronics"
    }
  ],
  "presupuesto": {
    "total": 8600000,
    "disponible": 15000000,
    "centroCosto": "CC-IT-001",
    "año": 2024
  },
  "aprobaciones": [
    {
      "nivel": "Jefe Directo",
      "aprobador": "Ana García",
      "fecha": "2024-01-16",
      "estado": "APROBADO",
      "observaciones": "Compra necesaria"
    },
    {
      "nivel": "Gerencia",
      "aprobador": "Luis Rodríguez",
      "fecha": "2024-01-17",
      "estado": "PENDIENTE"
    }
  ],
  "proveedores": {
    "cotizaciones": 3,
    "seleccionado": "Dell Colombia",
    "criterioSeleccion": "Mejor relación precio-calidad"
  }
}
```

### Inputs y Outputs de Tareas

#### Tarea de Revisión Legal
```json
// Inputs
{
  "documento": {
    "tipo": "CONTRATO",
    "version": "1.2",
    "url": "https://docs.empresa.com/contratos/CONT-2024-001.pdf"
  },
  "revision": {
    "tipo": "COMPLETA",
    "aspectos": [
      "clausulas_penales",
      "terminos_pago",
      "confidencialidad",
      "propiedad_intelectual"
    ],
    "urgencia": "ALTA"
  },
  "contexto": {
    "cliente": "Empresa XYZ",
    "valor": 500000000,
    "duracion": "12 meses"
  }
}

// Outputs
{
  "revision": {
    "completada": true,
    "fecha": "2024-01-15T16:30:00Z",
    "revisor": "Dra. Carmen López"
  },
  "hallazgos": [
    {
      "tipo": "OBSERVACION",
      "seccion": "Cláusula 5.2",
      "descripcion": "Términos de pago muy extensos",
      "recomendacion": "Reducir a 30 días",
      "criticidad": "MEDIA"
    },
    {
      "tipo": "RIESGO",
      "seccion": "Cláusula 8.1",
      "descripcion": "Penalidades excesivas",
      "recomendacion": "Negociar reducción del 50%",
      "criticidad": "ALTA"
    }
  ],
  "decision": {
    "aprobado": false,
    "requiereModificaciones": true,
    "observaciones": "Documento requiere ajustes importantes antes de firma"
  },
  "siguientesPasos": [
    "Negociar términos de pago",
    "Revisar cláusulas penales",
    "Solicitar nueva versión"
  ]
}
```

## 🏢 Escenarios Reales

### Escenario 1: Proceso de Facturación

**Contexto**: Empresa de servicios que necesita automatizar su proceso de facturación mensual.

**Plantilla de Proceso**:
```json
{
  "name": "Facturación Mensual",
  "description": "Proceso automatizado de facturación para clientes",
  "defaultVariables": "{
    \"periodo\": {
      \"año\": 2024,
      \"mes\": 1
    },
    \"clientes\": [],
    \"facturas\": [],
    \"totales\": {
      \"cantidad\": 0,
      \"valor\": 0
    }
  }"
}
```

**Tareas del Proceso**:
1. **Generar Facturas**: Crear facturas para todos los clientes activos
2. **Revisar Facturas**: Validar información y montos
3. **Enviar Facturas**: Distribuir facturas por email
4. **Seguimiento Pagos**: Monitorear pagos recibidos

### Escenario 2: Gestión de Incidentes IT

**Contexto**: Departamento de IT que maneja incidentes técnicos.

**Variables de Proceso**:
```json
{
  "incidente": {
    "numero": "INC-2024-0001",
    "titulo": "Servidor de aplicaciones no responde",
    "descripcion": "El servidor principal de aplicaciones no está respondiendo desde las 14:00",
    "reportadoPor": "<EMAIL>",
    "fechaReporte": "2024-01-15T14:15:00Z"
  },
  "clasificacion": {
    "categoria": "INFRAESTRUCTURA",
    "subcategoria": "SERVIDORES",
    "prioridad": "ALTA",
    "impacto": "ALTO",
    "urgencia": "ALTA"
  },
  "asignacion": {
    "grupo": "Infraestructura",
    "tecnico": "<EMAIL>",
    "fechaAsignacion": "2024-01-15T14:20:00Z"
  },
  "resolucion": {
    "estado": "EN_PROGRESO",
    "tiempoEstimado": "2 horas",
    "solucionTemporal": null,
    "solucionDefinitiva": null
  }
}
```

## 🔄 Patrones Comunes

### Patrón 1: Aprobación Multinivel

**Uso**: Procesos que requieren múltiples aprobaciones jerárquicas.

**Estructura**:
```json
{
  "aprobaciones": [
    {
      "nivel": 1,
      "rol": "Jefe Directo",
      "requerido": true,
      "completado": false
    },
    {
      "nivel": 2,
      "rol": "Gerente",
      "requerido": true,
      "completado": false
    },
    {
      "nivel": 3,
      "rol": "Director",
      "requerido": false,
      "completado": false
    }
  ]
}
```

### Patrón 2: Validación de Documentos

**Uso**: Procesos que requieren validación de múltiples documentos.

**Estructura**:
```json
{
  "documentos": [
    {
      "tipo": "CEDULA",
      "requerido": true,
      "recibido": true,
      "validado": true,
      "observaciones": "Documento válido"
    },
    {
      "tipo": "CERTIFICADO_INGRESOS",
      "requerido": true,
      "recibido": true,
      "validado": false,
      "observaciones": "Pendiente validación DIAN"
    }
  ]
}
```

### Patrón 3: Seguimiento de Estados

**Uso**: Procesos que necesitan tracking detallado de estados.

**Estructura**:
```json
{
  "historialEstados": [
    {
      "estado": "CREADO",
      "fecha": "2024-01-15T10:00:00Z",
      "usuario": "sistema",
      "observaciones": "Proceso iniciado automáticamente"
    },
    {
      "estado": "EN_REVISION",
      "fecha": "2024-01-15T10:30:00Z",
      "usuario": "analista.credito",
      "observaciones": "Asignado para revisión"
    }
  ]
}
```
