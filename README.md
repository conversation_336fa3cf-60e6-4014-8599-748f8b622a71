# Proezedure - Sistema de Gestión de Procesos

[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://openjdk.java.net/projects/jdk/21/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.5.3-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://www.postgresql.org/)
[![Tests](https://img.shields.io/badge/Tests-96%20passing-green.svg)](#testing)

## 📋 Descripción

**Proezedure** es un sistema robusto de gestión de procesos empresariales desarrollado con Spring Boot. Permite crear plantillas de procesos dinámicas, gestionar instancias de proceso con variables JSON, y administrar tareas secuenciales con inputs/outputs que se propagan automáticamente a las variables del proceso.

### 🎯 Características Principales

- **Plantillas de Proceso Dinámicas**: Definición flexible de procesos sin lógica preestablecida
- **Variables JSON Nativas**: Almacenamiento y manipulación de datos complejos en PostgreSQL
- **Tareas Secuenciales**: Gestión de tareas y subtareas con orden y dependencias
- **Propagación Automática**: Los outputs de tareas se propagan automáticamente a variables de proceso
- **Validaciones Robustas**: 96 tests unitarios garantizan la integridad del sistema
- **API REST Completa**: 50+ endpoints para todas las operaciones
- **Cliente Web Moderno**: Interfaz SPA con HTML/CSS/JS vanilla
- **Manejo de Excepciones**: Sistema robusto de validaciones y manejo de errores

## 🏗️ Arquitectura

```mermaid
graph TB
    subgraph "Capa de Presentación"
        WEB[Cliente Web SPA]
        API[API REST Controllers]
        EH[Exception Handlers]
    end
    
    subgraph "Capa de Negocio"
        PS[Process Services]
        TS[Task Services]
        VS[Variable Service]
        VLS[Validation Service]
    end
    
    subgraph "Capa de Datos"
        R[Repositories]
        E[Entities]
    end
    
    subgraph "Base de Datos"
        PG[(PostgreSQL)]
    end
    
    WEB --> API
    API --> PS
    API --> TS
    API --> VS
    EH --> API
    
    PS --> VLS
    TS --> VLS
    VS --> VLS
    
    PS --> R
    TS --> R
    VS --> R
    
    R --> E
    E --> PG
```

## 📝 Ejemplo de Uso

### Crear Proceso con Fecha de Vencimiento

```bash
# Crear una instancia de proceso con fecha de vencimiento
curl -X POST http://localhost:8080/api/process-instances \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Proceso de Aprobación",
    "description": "Proceso que debe completarse antes del fin de año",
    "dueDate": "2025-12-31",
    "variables": {
      "clienteId": "12345",
      "montoSolicitud": 50000
    }
  }'
```

### Actualizar Proceso con Nueva Fecha de Vencimiento

```bash
# Actualizar una instancia existente con nueva fecha de vencimiento
curl -X PUT http://localhost:8080/api/process-instances/{id} \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Proceso de Aprobación Actualizado",
    "description": "Proceso con fecha de vencimiento extendida",
    "dueDate": "2026-03-31",
    "variables": {
      "clienteId": "12345",
      "montoSolicitud": 75000
    }
  }'
```

## 🚀 Inicio Rápido

### Prerrequisitos

- Java 21+
- PostgreSQL 15+
- Maven 3.8+
- Docker (opcional, para desarrollo con Docker Compose)

### Instalación

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd proezedure
```

2. **Configurar la base de datos**
```sql
CREATE DATABASE proezedure;
CREATE USER proezedure_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE proezedure TO proezedure_user;
```

3. **Configurar variables de entorno**
```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=proezedure
export DB_USERNAME=proezedure_user
export DB_PASSWORD=your_password
```

4. **Ejecutar la aplicación**
```bash
# Desarrollo (con H2 en memoria)
./mvnw spring-boot:run

# Desarrollo (con Docker Compose para PostgreSQL)
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

La aplicación estará disponible en:
- **Cliente Web**: `http://localhost:8080`
- **API REST**: `http://localhost:8080/api`
- **Consola H2**: `http://localhost:8080/h2-console` (desarrollo)

### Migraciones de Base de Datos

El proyecto utiliza **Flyway** para gestionar las migraciones de base de datos de forma automática y versionada.

#### Características de Flyway en el Proyecto

- **Migraciones automáticas**: Se ejecutan al iniciar la aplicación
- **Versionado**: Cada migración tiene un número de versión único
- **Historial**: Se mantiene un registro de todas las migraciones aplicadas
- **Validación**: Verifica la integridad de las migraciones

#### Estructura de Migraciones

```
src/main/resources/db/migration/
├── V1__Initial_schema.sql          # Esquema inicial
└── V2__Add_due_date_to_process_instances.sql  # Agregar fecha de vencimiento
```

#### Comandos Útiles

```bash
# Ver estado de migraciones
./mvnw flyway:info

# Validar migraciones
./mvnw flyway:validate

# Limpiar base de datos (solo desarrollo)
./mvnw flyway:clean
```

### Configuración Alternativa

Puedes configurar la aplicación editando `src/main/resources/application.yml`:

```yaml
spring:
  datasource:
    url: *******************************************
    username: proezedure_user
    password: your_password
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
```

## 📊 Modelo de Datos

### Entidades Principales

```mermaid
erDiagram
    ProcessDefinition ||--o{ ProcessInstance : "template"
    ProcessInstance ||--o{ Task : "contains"
    Task ||--o{ Subtask : "contains"
    
    ProcessDefinition {
        UUID id PK
        string name
        string description
        string version
        json defaultVariables
        ProcessDefinitionStatus status
        timestamp createdAt
    }
    
    ProcessInstance {
        UUID id PK
        UUID templateId FK
        string name
        string description
        json variables
        ProcessStatus status
        date dueDate
        timestamp createdAt
        timestamp updatedAt
    }
    
    Task {
        UUID id PK
        UUID processInstanceId FK
        string name
        string description
        string formKey
        integer order
        string assignedTo
        json inputs
        json outputs
        TaskStatus status
        timestamp createdAt
        timestamp updatedAt
        timestamp claimedAt
        timestamp completedAt
    }
    
    Subtask {
        UUID id PK
        UUID taskId FK
        string name
        string description
        string formKey
        integer order
        json inputs
        json outputs
        SubtaskStatus status
        timestamp createdAt
        timestamp updatedAt
        timestamp completedAt
    }
```

## 🔄 Flujo de Trabajo Típico

1. **Crear Plantilla de Proceso**
   - Definir estructura básica con variables por defecto
   - Establecer estado ACTIVE

2. **Crear Instancia de Proceso**
   - Basada en una plantilla existente
   - Inicializar con variables específicas

3. **Gestionar Tareas**
   - Crear tareas secuenciales con inputs
   - Asignar a usuarios específicos
   - Completar tareas con outputs

4. **Propagación Automática**
   - Los outputs se propagan a variables de proceso
   - Merge inteligente preserva datos existentes

5. **Finalización**
   - Completar todas las tareas
   - Marcar proceso como COMPLETED

## 🧪 Testing

El sistema cuenta con **96 tests unitarios** que cubren:

- ✅ Servicios de negocio (65 tests)
- ✅ Validaciones robustas (20 tests)
- ✅ Utilidades JSON (15 tests)
- ✅ Repositorios JPA (5 tests)

```bash
# Ejecutar todos los tests
./mvnw test

# Ejecutar tests con reporte de cobertura
./mvnw test jacoco:report
```

## 🖥️ Cliente Web

El sistema incluye un **cliente web moderno** desarrollado con tecnologías estándar:

### Características
- **SPA (Single Page Application)** con router personalizado
- **Diseño responsivo** con paleta monocromática
- **Sin frameworks**: HTML5, CSS3, JavaScript ES6+ vanilla
- **Componentes**: Dashboard, gestión de procesos, tareas y plantillas

### Funcionalidades
- 📊 **Dashboard** con métricas y acciones rápidas
- 📋 **Plantillas** de proceso con filtros y búsqueda
- ⚙️ **Procesos** con indicadores de progreso y vencimiento
- ✓ **Tareas** con gestión completa y subtareas
- 👤 **Mis Tareas** personalizadas por usuario

## 📚 Documentación Adicional

- [📖 Documentación de API](docs/API.md) - Todos los endpoints REST
- [🏛️ Arquitectura del Sistema](docs/ARCHITECTURE.md) - Diseño técnico detallado
- [👨‍💻 Guía para Desarrolladores](docs/DEVELOPMENT.md) - Contribución y estándares
- [💼 Casos de Uso](docs/USE_CASES.md) - Ejemplos prácticos
- [🔧 Troubleshooting](docs/TROUBLESHOOTING.md) - Resolución de problemas

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

Ver [DEVELOPMENT.md](docs/DEVELOPMENT.md) para más detalles.

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 🏢 Desarrollado por

**Sucomunicación - GEDSYS2**  
Sistema de Gestión de Procesos Empresariales

---

Para más información, consulta la [documentación completa](docs/) o contacta al equipo de desarrollo.
