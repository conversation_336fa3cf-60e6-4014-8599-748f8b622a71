-- Initial schema creation for Proezedure application
-- This migration creates all the base tables and their relationships

-- Create process_definitions table
CREATE TABLE process_definitions (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL,
    default_variables JSON,
    status VARCHAR(20) NOT NULL CHECK (status IN ('ACTIVE', 'INACTIVE')),
    created_at TIMESTAMP NOT NULL
);

-- Create process_instances table
CREATE TABLE process_instances (
    id UUID PRIMARY KEY,
    template_id UUID,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    variables JSON,
    status VARCHAR(20) NOT NULL CHECK (status IN ('RUNNING', 'COMPLETED', 'CANCELLED')),
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- Create tasks table
CREATE TABLE tasks (
    id UUID PRIMARY KEY,
    process_instance_id UUID NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    form_key VARCHAR(255) NOT NULL,
    order_number INTEGER NOT NULL,
    assigned_to VARCHAR(255),
    status VARCHAR(20) NOT NULL CHECK (status IN ('CREATED', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED')),
    inputs JSON,
    outputs JSON,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    claimed_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- Create subtasks table
CREATE TABLE subtasks (
    id UUID PRIMARY KEY,
    task_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    form_key VARCHAR(255) NOT NULL,
    order_number INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('CREATED', 'COMPLETED', 'CANCELLED')),
    inputs JSON,
    outputs JSON,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP
);

-- Add foreign key constraints
ALTER TABLE process_instances 
    ADD CONSTRAINT fk_process_instances_template_id 
    FOREIGN KEY (template_id) REFERENCES process_definitions(id);

ALTER TABLE tasks 
    ADD CONSTRAINT fk_tasks_process_instance_id 
    FOREIGN KEY (process_instance_id) REFERENCES process_instances(id);

ALTER TABLE subtasks 
    ADD CONSTRAINT fk_subtasks_task_id 
    FOREIGN KEY (task_id) REFERENCES tasks(id);

-- Create indexes for better performance
CREATE INDEX idx_process_instances_template_id ON process_instances(template_id);
CREATE INDEX idx_process_instances_status ON process_instances(status);
CREATE INDEX idx_process_instances_created_at ON process_instances(created_at);

CREATE INDEX idx_tasks_process_instance_id ON tasks(process_instance_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_order ON tasks(order_number);

CREATE INDEX idx_subtasks_task_id ON subtasks(task_id);
CREATE INDEX idx_subtasks_status ON subtasks(status);
CREATE INDEX idx_subtasks_order ON subtasks(order_number);

CREATE INDEX idx_process_definitions_status ON process_definitions(status);
CREATE INDEX idx_process_definitions_name ON process_definitions(name);

-- Add comments for documentation
COMMENT ON TABLE process_definitions IS 'Plantillas de procesos que definen la estructura base';
COMMENT ON TABLE process_instances IS 'Instancias de procesos en ejecución o completados';
COMMENT ON TABLE tasks IS 'Tareas individuales dentro de una instancia de proceso';
COMMENT ON TABLE subtasks IS 'Subtareas que componen una tarea principal';

COMMENT ON COLUMN process_definitions.default_variables IS 'Variables por defecto en formato JSON';
COMMENT ON COLUMN process_instances.variables IS 'Variables específicas de la instancia en formato JSON';
COMMENT ON COLUMN tasks.inputs IS 'Datos de entrada de la tarea en formato JSON';
COMMENT ON COLUMN tasks.outputs IS 'Datos de salida de la tarea en formato JSON';
COMMENT ON COLUMN subtasks.inputs IS 'Datos de entrada de la subtarea en formato JSON';
COMMENT ON COLUMN subtasks.outputs IS 'Datos de salida de la subtarea en formato JSON';
