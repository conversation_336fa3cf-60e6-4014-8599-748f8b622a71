package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.Subtask;
import co.com.gedsys.proezedure.domain.SubtaskStatus;
import co.com.gedsys.proezedure.exception.SubtaskNotFoundException;
import co.com.gedsys.proezedure.repository.SubtaskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SubtaskServiceTest {

    @Mock
    private SubtaskRepository subtaskRepository;

    @Mock
    private ValidationService validationService;

    @InjectMocks
    private SubtaskService subtaskService;

    private Subtask testSubtask;
    private UUID subtaskId;
    private UUID taskId;

    @BeforeEach
    void setUp() {
        subtaskId = UUID.randomUUID();
        taskId = UUID.randomUUID();
        
        testSubtask = new Subtask(taskId, "Test Subtask", "Description", "subtask-form-key-1", 1, "{}");
        testSubtask.setId(subtaskId);
    }

    @Test
    void shouldCreateSubtask() {
        // Given
        when(subtaskRepository.save(any(Subtask.class))).thenReturn(testSubtask);

        // When
        Subtask result = subtaskService.create(taskId, "Test Subtask", "Description", "subtask-form-key-1", 1, "{}");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Subtask");
        assertThat(result.getTaskId()).isEqualTo(taskId);
        verify(subtaskRepository).save(any(Subtask.class));
    }

    @Test
    void shouldFindSubtaskById() {
        // Given
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));

        // When
        Subtask result = subtaskService.findById(subtaskId);

        // Then
        assertThat(result).isEqualTo(testSubtask);
        verify(subtaskRepository).findById(subtaskId);
    }

    @Test
    void shouldThrowExceptionWhenSubtaskNotFound() {
        // Given
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> subtaskService.findById(subtaskId))
                .isInstanceOf(SubtaskNotFoundException.class)
                .hasMessageContaining(subtaskId.toString());

        verify(subtaskRepository).findById(subtaskId);
    }

    @Test
    void shouldCompleteSubtask() {
        // Given
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));
        when(subtaskRepository.save(testSubtask)).thenReturn(testSubtask);

        // When
        Subtask result = subtaskService.complete(subtaskId, "{\"result\": \"success\"}");

        // Then
        assertThat(result.getStatus()).isEqualTo(SubtaskStatus.COMPLETED);
        assertThat(result.getOutputs()).isEqualTo("{\"result\": \"success\"}");
        assertThat(result.getCompletedAt()).isNotNull();
        verify(subtaskRepository).findById(subtaskId);
        verify(subtaskRepository).save(testSubtask);
    }

    @Test
    void shouldUpdateSubtaskStatus() {
        // Given
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));
        when(subtaskRepository.save(testSubtask)).thenReturn(testSubtask);

        // When
        Subtask result = subtaskService.updateStatus(subtaskId, SubtaskStatus.CANCELLED);

        // Then
        assertThat(result.getStatus()).isEqualTo(SubtaskStatus.CANCELLED);
        verify(subtaskRepository).findById(subtaskId);
        verify(subtaskRepository).save(testSubtask);
    }

    @Test
    void shouldUpdateSubtaskInputs() {
        // Given
        String newInputs = "{\"updated\": \"inputs\"}";
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));
        when(subtaskRepository.save(testSubtask)).thenReturn(testSubtask);

        // When
        Subtask result = subtaskService.updateInputs(subtaskId, newInputs);

        // Then
        assertThat(result.getInputs()).isEqualTo(newInputs);
        verify(subtaskRepository).findById(subtaskId);
        verify(subtaskRepository).save(testSubtask);
    }

    @Test
    void shouldUpdateSubtaskOutputs() {
        // Given
        String newOutputs = "{\"updated\": \"outputs\"}";
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));
        when(subtaskRepository.save(testSubtask)).thenReturn(testSubtask);

        // When
        Subtask result = subtaskService.updateOutputs(subtaskId, newOutputs);

        // Then
        assertThat(result.getOutputs()).isEqualTo(newOutputs);
        verify(subtaskRepository).findById(subtaskId);
        verify(subtaskRepository).save(testSubtask);
    }

    @Test
    void shouldDeleteSubtask() {
        // Given
        when(subtaskRepository.findById(subtaskId)).thenReturn(Optional.of(testSubtask));

        // When
        subtaskService.delete(subtaskId);

        // Then
        verify(subtaskRepository).findById(subtaskId);
        verify(subtaskRepository).delete(testSubtask);
    }

    @Test
    void shouldCheckIfAllSubtasksCompleted() {
        // Given
        when(subtaskRepository.areAllSubtasksCompleted(taskId)).thenReturn(true);

        // When
        boolean result = subtaskService.areAllSubtasksCompleted(taskId);

        // Then
        assertThat(result).isTrue();
        verify(subtaskRepository).areAllSubtasksCompleted(taskId);
    }
}
