package co.com.gedsys.proezedure.repository;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.TemplateStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProcessDefinitionRepository extends JpaRepository<ProcessDefinition, UUID> {

    List<ProcessDefinition> findByStatus(TemplateStatus status);

    List<ProcessDefinition> findByNameContainingIgnoreCase(String name);

    Optional<ProcessDefinition> findByNameAndVersion(String name, String version);

    @Query("SELECT pd FROM ProcessDefinition pd WHERE pd.status = :status ORDER BY pd.createdAt DESC")
    List<ProcessDefinition> findActiveDefinitionsOrderByCreatedAt(@Param("status") TemplateStatus status);

    @Query("SELECT COUNT(pd) FROM ProcessDefinition pd WHERE pd.status = :status")
    long countByStatus(@Param("status") TemplateStatus status);

    boolean existsByNameAndVersion(String name, String version);
}
