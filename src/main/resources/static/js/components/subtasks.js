/**
 * Componente Subtasks - Gestión de subtareas
 */
window.SubtasksComponent = {
  name: 'subtasks',
  currentTaskId: null,
  currentSubtasks: [],
  
  async render(params = {}) {
    try {
      DOMUtils.show('loading');
      
      // Obtener ID de la tarea desde parámetros
      this.currentTaskId = params.task || new URLSearchParams(window.location.search).get('task');
      
      if (this.currentTaskId) {
        // Obtener subtareas de una tarea específica
        this.currentSubtasks = await API.tasks.getSubtasks(this.currentTaskId);
        const taskInfo = await API.tasks.getById(this.currentTaskId);
        
        DOMUtils.updateBreadcrumb([
          { text: 'Inicio', href: '#/' },
          { text: 'Tareas', href: '#/tasks' },
          { text: taskInfo.name, href: `#/tasks/${this.currentTaskId}` },
          { text: 'Subtareas' }
        ]);
        
        const content = document.getElementById('app-content');
        content.innerHTML = this.getTaskSubtasksTemplate(taskInfo);
      } else {
        // Obtener todas las subtareas
        this.currentSubtasks = await API.subtasks.getAll();
        
        DOMUtils.updateBreadcrumb([
          { text: 'Inicio', href: '#/' },
          { text: 'Subtareas' }
        ]);
        
        const content = document.getElementById('app-content');
        content.innerHTML = this.getAllSubtasksTemplate();
      }
      
      DOMUtils.hide('loading');
      
      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando subtareas');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTaskSubtasksTemplate(taskInfo) {
    return `
      <div class="subtasks">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Subtareas</h1>
              <p class="text-gray-600">Subtareas de: <strong>${DOMUtils.escapeHtml(taskInfo.name)}</strong></p>
            </div>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Nueva Subtarea
            </button>
          </div>
        </div>

        <!-- Información de la tarea padre -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-3">
              <div>
                <strong>Estado:</strong> ${Formatters.formatStatus(taskInfo.status)}
              </div>
              <div>
                <strong>Asignada a:</strong> ${Formatters.formatUsername(taskInfo.assignedTo)}
              </div>
              <div>
                <strong>Orden:</strong> ${taskInfo.order}
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de subtareas -->
        <div class="subtasks-list" id="subtasks-list">
          ${this.getSubtasksListTemplate()}
        </div>
      </div>

      ${this.getStyles()}
    `;
  },

  getAllSubtasksTemplate() {
    return `
      <div class="subtasks">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Todas las Subtareas</h1>
              <p class="text-gray-600">Gestiona todas las subtareas del sistema</p>
            </div>
          </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-3">
              <div class="form-group">
                <label class="form-label">Buscar</label>
                <input type="text" class="form-input" id="search-input" placeholder="Buscar por nombre...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Estado</label>
                <select class="form-select" id="status-filter">
                  <option value="">Todos los estados</option>
                  <option value="CREATED">Creada</option>
                  <option value="COMPLETED">Completada</option>
                  <option value="CANCELLED">Cancelada</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Ordenar por</label>
                <select class="form-select" id="sort-select">
                  <option value="order">Orden</option>
                  <option value="name">Nombre</option>
                  <option value="status">Estado</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de subtareas -->
        <div class="subtasks-list" id="subtasks-list">
          ${this.getSubtasksListTemplate()}
        </div>
      </div>

      ${this.getStyles()}
    `;
  },

  getSubtasksListTemplate() {
    if (this.currentSubtasks.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <h3>No hay subtareas</h3>
            <p class="text-gray-600 mb-4">${this.currentTaskId ? 'Esta tarea no tiene subtareas.' : 'No se encontraron subtareas en el sistema.'}</p>
            ${this.currentTaskId ? `
              <button class="btn btn-primary" data-action="create">
                <span>➕</span>
                Crear Primera Subtarea
              </button>
            ` : ''}
          </div>
        </div>
      `;
    }

    return this.currentSubtasks.map(subtask => `
      <div class="subtask-card" data-id="${subtask.id}">
        <div class="subtask-header">
          <div>
            <h3 class="subtask-title">${DOMUtils.escapeHtml(subtask.name)}</h3>
            <div class="subtask-meta">
              <span class="subtask-order">Orden ${subtask.order}</span>
              ${subtask.formKey ? ` • Form: ${subtask.formKey}` : ''}
            </div>
          </div>
          <div class="subtask-actions">
            ${subtask.status === 'CREATED' ? 
              `<button class="btn btn-sm btn-success" data-action="complete" data-id="${subtask.id}">Completar</button>` : 
              ''
            }
            <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${subtask.id}">
              Editar
            </button>
            <button class="btn btn-sm btn-outline" data-action="delete" data-id="${subtask.id}" style="color: var(--color-gray-700);">
              Eliminar
            </button>
          </div>
        </div>
        
        ${subtask.description ? `<div class="subtask-description">${DOMUtils.escapeHtml(subtask.description)}</div>` : ''}
        
        <div class="subtask-footer">
          <div>
            ${Formatters.formatStatus(subtask.status)}
          </div>
          <div class="subtask-meta">
            ${subtask.completedAt ? `Completada: ${Formatters.formatRelativeTime(subtask.completedAt)}` : `Creada: ${Formatters.formatRelativeTime(subtask.createdAt)}`}
          </div>
        </div>
      </div>
    `).join('');
  },

  getStyles() {
    return `
      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .subtask-card {
          background: var(--color-white);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius-lg);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          transition: var(--transition);
        }
        
        .subtask-card:hover {
          box-shadow: var(--shadow-md);
        }
        
        .subtask-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-3);
        }
        
        .subtask-title {
          font-size: var(--font-size-lg);
          font-weight: 600;
          color: var(--color-gray-800);
          margin-bottom: var(--spacing-1);
        }
        
        .subtask-meta {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
        }
        
        .subtask-actions {
          display: flex;
          gap: var(--spacing-2);
        }
        
        .subtask-description {
          color: var(--color-gray-600);
          margin-bottom: var(--spacing-3);
        }
        
        .subtask-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: var(--spacing-3);
          border-top: 1px solid var(--color-gray-200);
        }
        
        .subtask-order {
          background-color: var(--color-gray-100);
          color: var(--color-gray-700);
          padding: var(--spacing-1) var(--spacing-2);
          border-radius: var(--border-radius);
          font-size: var(--font-size-sm);
          font-weight: 500;
        }
      </style>
    `;
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar subtareas</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener las subtareas.</p>
          <button class="btn btn-primary" onclick="window.SubtasksComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  setupEventListeners() {
    // Búsqueda y filtros (solo para vista de todas las subtareas)
    if (!this.currentTaskId) {
      const searchInput = document.getElementById('search-input');
      const statusFilter = document.getElementById('status-filter');
      const sortSelect = document.getElementById('sort-select');
      
      if (searchInput) {
        searchInput.addEventListener('input', DOMUtils.debounce(() => this.filterSubtasks(), 300));
      }
      
      [statusFilter, sortSelect].forEach(element => {
        if (element) {
          element.addEventListener('change', () => this.filterSubtasks());
        }
      });
    }

    // Acciones de botones
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const id = actionButton.dataset.id;

      this.handleAction(action, id);
    });
  },

  filterSubtasks() {
    // Implementar filtrado similar a otros componentes
    console.log('Filtrar subtareas');
  },

  async handleAction(action, id) {
    try {
      switch (action) {
        case 'create':
          this.showCreateModal();
          break;
        case 'edit':
          this.showEditModal(id);
          break;
        case 'complete':
          await this.completeSubtask(id);
          break;
        case 'delete':
          await this.deleteSubtask(id);
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  showCreateModal() {
    if (!this.currentTaskId) {
      window.NotificationManager.error('Se requiere una tarea específica para crear subtareas');
      return;
    }

    const modalContent = `
      <form id="create-subtask-form">
        <div class="form-group">
          <label class="form-label" for="subtask-name">Nombre *</label>
          <input type="text" class="form-input" id="subtask-name" name="name" required
                 placeholder="Nombre de la subtarea">
        </div>

        <div class="form-group">
          <label class="form-label" for="subtask-description">Descripción</label>
          <textarea class="form-textarea" id="subtask-description" name="description"
                    placeholder="Descripción de la subtarea" rows="3"></textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="subtask-form-key">Form Key</label>
          <input type="text" class="form-input" id="subtask-form-key" name="formKey"
                 placeholder="form_key_subtarea">
          <small class="form-help">Identificador del formulario asociado (opcional).</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="subtask-order">Orden *</label>
          <input type="number" class="form-input" id="subtask-order" name="order" required
                 min="1" value="1">
          <small class="form-help">Orden de ejecución de la subtarea.</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="subtask-inputs">Inputs (JSON)</label>
          <textarea class="form-textarea" id="subtask-inputs" name="inputs"
                    placeholder='{"input1": "valor1", "input2": "valor2"}' rows="3"></textarea>
          <small class="form-help">Datos de entrada para la subtarea en formato JSON.</small>
        </div>
      </form>
    `;

    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="save-subtask-btn">Crear Subtarea</button>
    `;

    ModalManager.show('Nueva Subtarea', modalContent, modalFooter);

    // Event listener para guardar
    document.getElementById('save-subtask-btn').addEventListener('click', async () => {
      await this.handleCreateSubmit();
    });
  },

  async handleCreateSubmit() {
    const form = document.getElementById('create-subtask-form');
    if (!DOMUtils.validateForm(form)) return;

    try {
      const formData = new FormData(form);
      const data = {
        taskId: this.currentTaskId,
        name: formData.get('name'),
        description: formData.get('description') || null,
        formKey: formData.get('formKey') || null,
        order: parseInt(formData.get('order')),
        inputs: this.parseJsonField(formData.get('inputs'))
      };

      await API.subtasks.create(data);

      ModalManager.hide();
      window.NotificationManager.success('Subtarea creada exitosamente');
      await this.render({ task: this.currentTaskId });

    } catch (error) {
      API.utils.handleError(error, 'creando subtarea');
    }
  },

  showEditModal(id) {
    const subtask = this.currentSubtasks.find(st => st.id === id);
    if (!subtask) {
      window.NotificationManager.error('Subtarea no encontrada');
      return;
    }

    const inputs = subtask.inputs ?
      (typeof subtask.inputs === 'string' ?
        subtask.inputs :
        JSON.stringify(subtask.inputs, null, 2)) : '';

    const outputs = subtask.outputs ?
      (typeof subtask.outputs === 'string' ?
        subtask.outputs :
        JSON.stringify(subtask.outputs, null, 2)) : '';

    const modalContent = `
      <form id="edit-subtask-form">
        <div class="form-group">
          <label class="form-label" for="edit-subtask-name">Nombre *</label>
          <input type="text" class="form-input" id="edit-subtask-name" name="name" required
                 value="${DOMUtils.escapeHtml(subtask.name)}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-subtask-description">Descripción</label>
          <textarea class="form-textarea" id="edit-subtask-description" name="description"
                    rows="3">${DOMUtils.escapeHtml(subtask.description || '')}</textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-subtask-form-key">Form Key</label>
          <input type="text" class="form-input" id="edit-subtask-form-key" name="formKey"
                 value="${DOMUtils.escapeHtml(subtask.formKey || '')}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-subtask-order">Orden *</label>
          <input type="number" class="form-input" id="edit-subtask-order" name="order" required
                 min="1" value="${subtask.order}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-subtask-status">Estado</label>
          <select class="form-select" id="edit-subtask-status" name="status">
            <option value="CREATED" ${subtask.status === 'CREATED' ? 'selected' : ''}>Creada</option>
            <option value="COMPLETED" ${subtask.status === 'COMPLETED' ? 'selected' : ''}>Completada</option>
            <option value="CANCELLED" ${subtask.status === 'CANCELLED' ? 'selected' : ''}>Cancelada</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-subtask-inputs">Inputs (JSON)</label>
          <textarea class="form-textarea" id="edit-subtask-inputs" name="inputs"
                    rows="3">${DOMUtils.escapeHtml(inputs)}</textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-subtask-outputs">Outputs (JSON)</label>
          <textarea class="form-textarea" id="edit-subtask-outputs" name="outputs"
                    rows="3">${DOMUtils.escapeHtml(outputs)}</textarea>
          <small class="form-help">Datos de salida de la subtarea.</small>
        </div>
      </form>
    `;

    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="update-subtask-btn">Actualizar Subtarea</button>
    `;

    ModalManager.show('Editar Subtarea', modalContent, modalFooter);

    // Event listener para actualizar
    document.getElementById('update-subtask-btn').addEventListener('click', async () => {
      await this.handleEditSubmit(id);
    });
  },

  async handleEditSubmit(id) {
    const form = document.getElementById('edit-subtask-form');
    if (!DOMUtils.validateForm(form)) return;

    try {
      const formData = new FormData(form);
      const data = {
        name: formData.get('name'),
        description: formData.get('description') || null,
        formKey: formData.get('formKey') || null,
        order: parseInt(formData.get('order')),
        status: formData.get('status'),
        inputs: this.parseJsonField(formData.get('inputs')),
        outputs: this.parseJsonField(formData.get('outputs'))
      };

      await API.subtasks.update(id, data);

      ModalManager.hide();
      window.NotificationManager.success('Subtarea actualizada exitosamente');
      await this.render({ task: this.currentTaskId });

    } catch (error) {
      API.utils.handleError(error, 'actualizando subtarea');
    }
  },

  async completeSubtask(id) {
    if (confirm('¿Estás seguro de que quieres completar esta subtarea?')) {
      try {
        await API.subtasks.complete(id);
        window.NotificationManager.success('Subtarea completada exitosamente');
        await this.render({ task: this.currentTaskId });
      } catch (error) {
        API.utils.handleError(error, 'completando subtarea');
      }
    }
  },

  async deleteSubtask(id) {
    const subtask = this.currentSubtasks.find(st => st.id === id);
    if (!subtask) {
      window.NotificationManager.error('Subtarea no encontrada');
      return;
    }

    if (confirm(`¿Estás seguro de que quieres eliminar la subtarea "${subtask.name}"? Esta acción no se puede deshacer.`)) {
      try {
        await API.subtasks.delete(id);
        window.NotificationManager.success('Subtarea eliminada exitosamente');
        await this.render({ task: this.currentTaskId });
      } catch (error) {
        API.utils.handleError(error, 'eliminando subtarea');
      }
    }
  },

  parseJsonField(jsonString) {
    if (!jsonString || !jsonString.trim()) {
      return null;
    }

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      throw new Error('El formato JSON no es válido');
    }
  }
};
