/**
 * Componente My Tasks - Vista de tareas del usuario actual
 */
window.MyTasksComponent = {
  name: 'my-tasks',
  currentUser: 'usuario', // En una implementación real, esto vendría de autenticación
  userTasks: {
    pending: [],
    inProgress: [],
    completed: []
  },
  
  async render() {
    try {
      DOMUtils.show('loading');
      
      // Obtener tareas del usuario
      const [pending, inProgress, completed] = await Promise.all([
        API.userTasks.getPending(this.currentUser),
        API.userTasks.getInProgress(this.currentUser),
        API.userTasks.getCompleted(this.currentUser)
      ]);
      
      this.userTasks = { pending, inProgress, completed };
      
      DOMUtils.hide('loading');
      
      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Inicio', href: '#/' },
        { text: 'Mis Tareas' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate();

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando mis tareas');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate() {
    const totalTasks = this.userTasks.pending.length + this.userTasks.inProgress.length + this.userTasks.completed.length;
    
    return `
      <div class="my-tasks">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Mis Tareas</h1>
              <p class="text-gray-600">Gestiona tus tareas asignadas (${totalTasks} total)</p>
            </div>
            <div class="user-selector">
              <label class="form-label">Usuario:</label>
              <input type="text" class="form-input" id="username-input" value="${this.currentUser}" placeholder="Nombre de usuario">
              <button class="btn btn-secondary" id="change-user-btn">Cambiar</button>
            </div>
          </div>
        </div>

        <!-- Resumen de tareas -->
        <div class="grid grid-cols-3 mb-6">
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value text-warning">${this.userTasks.pending.length}</div>
              <div class="metric-label">Pendientes</div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value text-primary">${this.userTasks.inProgress.length}</div>
              <div class="metric-label">En Progreso</div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value text-success">${this.userTasks.completed.length}</div>
              <div class="metric-label">Completadas</div>
            </div>
          </div>
        </div>

        <!-- Pestañas de tareas -->
        <div class="tabs-container">
          <div class="tabs-header">
            <button class="tab-button active" data-tab="pending">
              Pendientes (${this.userTasks.pending.length})
            </button>
            <button class="tab-button" data-tab="in-progress">
              En Progreso (${this.userTasks.inProgress.length})
            </button>
            <button class="tab-button" data-tab="completed">
              Completadas (${this.userTasks.completed.length})
            </button>
          </div>
          
          <div class="tabs-content">
            <div class="tab-panel active" id="tab-pending">
              ${this.getTasksTabTemplate(this.userTasks.pending, 'pending')}
            </div>
            
            <div class="tab-panel" id="tab-in-progress">
              ${this.getTasksTabTemplate(this.userTasks.inProgress, 'in-progress')}
            </div>
            
            <div class="tab-panel" id="tab-completed">
              ${this.getTasksTabTemplate(this.userTasks.completed, 'completed')}
            </div>
          </div>
        </div>
      </div>

      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .user-selector {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
        }
        
        .user-selector .form-input {
          width: 200px;
        }
        
        .metric-value {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
        }
        
        .metric-value.text-warning {
          color: var(--color-gray-500);
        }
        
        .metric-value.text-primary {
          color: var(--color-gray-700);
        }
        
        .metric-value.text-success {
          color: var(--color-gray-800);
        }
        
        .metric-label {
          font-size: 0.875rem;
          color: var(--color-gray-600);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .tabs-container {
          background: var(--color-white);
          border-radius: var(--border-radius-lg);
          box-shadow: var(--shadow);
          overflow: hidden;
        }
        
        .tabs-header {
          display: flex;
          background-color: var(--color-gray-50);
          border-bottom: 1px solid var(--color-gray-200);
        }
        
        .tab-button {
          flex: 1;
          padding: var(--spacing-4);
          border: none;
          background: none;
          font-weight: 500;
          color: var(--color-gray-600);
          cursor: pointer;
          transition: var(--transition);
        }
        
        .tab-button:hover {
          background-color: var(--color-gray-100);
        }
        
        .tab-button.active {
          background-color: var(--color-white);
          color: var(--color-primary);
          border-bottom: 2px solid var(--color-primary);
        }
        
        .tabs-content {
          min-height: 400px;
        }
        
        .tab-panel {
          display: none;
          padding: var(--spacing-4);
        }
        
        .tab-panel.active {
          display: block;
        }
        
        .task-item {
          background: var(--color-gray-50);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-3);
          transition: var(--transition);
        }
        
        .task-item:hover {
          box-shadow: var(--shadow-sm);
        }
        
        .task-item-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-2);
        }
        
        .task-item-title {
          font-weight: 600;
          color: var(--color-gray-800);
          margin-bottom: var(--spacing-1);
        }
        
        .task-item-meta {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
        }
        
        .task-item-actions {
          display: flex;
          gap: var(--spacing-2);
        }
      </style>
    `;
  },

  getTasksTabTemplate(tasks, type) {
    if (tasks.length === 0) {
      const messages = {
        'pending': 'No tienes tareas pendientes',
        'in-progress': 'No tienes tareas en progreso',
        'completed': 'No has completado tareas aún'
      };
      
      return `
        <div class="text-center py-8">
          <p class="text-gray-500">${messages[type]}</p>
        </div>
      `;
    }

    return tasks.map(task => `
      <div class="task-item" data-id="${task.id}">
        <div class="task-item-header">
          <div>
            <div class="task-item-title">${DOMUtils.escapeHtml(task.name)}</div>
            <div class="task-item-meta">
              Orden ${task.order} • Form: ${task.formKey || 'N/A'}
            </div>
          </div>
          <div class="task-item-actions">
            ${type === 'pending' ?
              `<button class="btn btn-sm btn-primary" data-action="claim" data-id="${task.id}">Reclamar</button>` :
              ''
            }
            ${type === 'in-progress' ?
              `<button class="btn btn-sm btn-success" data-action="complete" data-id="${task.id}">Completar</button>` :
              ''
            }
            <button class="btn btn-sm btn-outline" data-action="view-subtasks" data-id="${task.id}">Subtareas</button>
            <button class="btn btn-sm btn-outline" data-action="view" data-id="${task.id}">Ver</button>
          </div>
        </div>
        
        ${task.description ? `<div class="task-item-description">${DOMUtils.escapeHtml(task.description)}</div>` : ''}
        
        <div class="task-item-footer">
          ${Formatters.formatStatus(task.status)}
        </div>
      </div>
    `).join('');
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar mis tareas</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener tus tareas.</p>
          <button class="btn btn-primary" onclick="window.MyTasksComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  setupEventListeners() {
    // Cambio de usuario
    const changeUserBtn = document.getElementById('change-user-btn');
    const usernameInput = document.getElementById('username-input');
    
    if (changeUserBtn && usernameInput) {
      changeUserBtn.addEventListener('click', () => {
        const newUsername = usernameInput.value.trim();
        if (newUsername && newUsername !== this.currentUser) {
          this.currentUser = newUsername;
          this.render();
        }
      });
      
      usernameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          changeUserBtn.click();
        }
      });
    }

    // Pestañas
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const tabName = e.target.dataset.tab;
        this.switchTab(tabName);
      });
    });

    // Acciones de tareas
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const id = actionButton.dataset.id;

      this.handleTaskAction(action, id);
    });
  },

  switchTab(tabName) {
    // Desactivar todas las pestañas
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
    
    // Activar la pestaña seleccionada
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`tab-${tabName}`).classList.add('active');
  },

  async handleTaskAction(action, id) {
    try {
      switch (action) {
        case 'claim':
          await this.claimTask(id);
          break;
        case 'complete':
          await this.completeTask(id);
          break;
        case 'view-subtasks':
          this.viewTaskSubtasks(id);
          break;
        case 'view':
          this.viewTask(id);
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  async claimTask(id) {
    try {
      await API.tasks.claim(id, this.currentUser);
      window.NotificationManager.success('Tarea reclamada exitosamente');
      await this.render(); // Recargar la vista
    } catch (error) {
      API.utils.handleError(error, 'reclamando tarea');
    }
  },

  async completeTask(id) {
    if (confirm('¿Estás seguro de que quieres completar esta tarea?')) {
      await API.tasks.complete(id);
      await this.render(); // Recargar la vista
    }
  },

  viewTaskSubtasks(id) {
    if (window.Router) {
      window.Router.navigate(`/subtasks?task=${id}`);
    } else {
      window.location.hash = `#/subtasks?task=${id}`;
    }
  },

  viewTask(id) {
    if (window.Router) {
      window.Router.navigate(`/tasks/${id}`);
    } else {
      window.location.hash = `#/tasks/${id}`;
    }
  }
};
