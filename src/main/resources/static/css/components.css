/* ===== COMPONENTES ESPECÍFICOS ===== */

/* ===== BOTONES ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border: var(--border-width) solid transparent;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  background-color: transparent;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-gray-800);
  color: var(--color-white);
  border-color: var(--color-gray-800);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-gray-700);
  border-color: var(--color-gray-700);
}

.btn-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-400);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-400);
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-lg);
}

/* ===== FORMULARIOS ===== */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  color: var(--color-gray-700);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: var(--border-width) solid var(--color-gray-300);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  transition: var(--transition);
  background-color: var(--color-white);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-gray-500);
  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-error {
  color: var(--color-gray-700);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
}

.form-help {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  margin-top: var(--spacing-1);
  display: block;
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--color-gray-700);
  box-shadow: 0 0 0 3px rgba(52, 58, 64, 0.1);
}

/* ===== TABLAS ===== */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-4);
}

.table th,
.table td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: var(--border-width) solid var(--color-gray-200);
}

.table th {
  background-color: var(--color-gray-50);
  font-weight: 600;
  color: var(--color-gray-700);
}

.table tbody tr:hover {
  background-color: var(--color-gray-50);
}

.table-responsive {
  overflow-x: auto;
}

/* ===== TARJETAS ===== */
.card {
  background-color: var(--color-white);
  border: var(--border-width) solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-4);
  background-color: var(--color-gray-50);
  border-bottom: var(--border-width) solid var(--color-gray-200);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-800);
  margin: 0;
}

.card-body {
  padding: var(--spacing-4);
}

.card-footer {
  padding: var(--spacing-4);
  background-color: var(--color-gray-50);
  border-top: var(--border-width) solid var(--color-gray-200);
}

/* ===== BADGES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: 500;
  border-radius: var(--border-radius);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-primary {
  background-color: var(--color-gray-800);
  color: var(--color-white);
}

.badge-secondary {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
}

.badge-success {
  background-color: var(--color-gray-700);
  color: var(--color-white);
}

.badge-warning {
  background-color: var(--color-gray-500);
  color: var(--color-white);
}

/* ===== MODAL ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: var(--border-width) solid var(--color-gray-200);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-800);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--color-gray-500);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--color-gray-700);
}

.modal-body {
  padding: var(--spacing-4);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  border-top: var(--border-width) solid var(--color-gray-200);
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 1100;
  max-width: 350px;
}

.toast {
  background-color: var(--color-white);
  border: var(--border-width) solid var(--color-gray-200);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-2);
  animation: slideIn 0.3s ease-out;
}

.toast-success {
  border-left: 4px solid var(--color-gray-700);
}

.toast-error {
  border-left: 4px solid var(--color-gray-800);
}

.toast-warning {
  border-left: 4px solid var(--color-gray-500);
}

.toast-info {
  border-left: 4px solid var(--color-gray-600);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== GRID LAYOUT ===== */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
  }
  
  .toast-container {
    left: var(--spacing-4);
    right: var(--spacing-4);
    max-width: none;
  }
}
