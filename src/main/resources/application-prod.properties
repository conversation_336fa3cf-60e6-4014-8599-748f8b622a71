# Production Profile
spring.docker.compose.enabled=false

# Database Configuration (should be configured via environment variables)
# spring.datasource.url=${DATABASE_URL}
# spring.datasource.username=${DATABASE_USERNAME}
# spring.datasource.password=${DATABASE_PASSWORD}

# JPA Configuration for Production
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Logging for Production
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.root=INFO
