package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;
import co.com.gedsys.proezedure.dto.*;
import co.com.gedsys.proezedure.service.TaskService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/tasks")
public class TaskController {

    private final TaskService taskService;

    public TaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @PostMapping
    public ResponseEntity<TaskResponse> create(@Valid @RequestBody CreateTaskRequest request) {
        Task task = taskService.create(
            request.getProcessInstanceId(),
            request.getName(),
            request.getDescription(),
            request.getFormKey(),
            request.getOrder(),
            request.getInputs()
        );
        
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping
    public ResponseEntity<List<TaskResponse>> findAll(
            @RequestParam(required = false) TaskStatus status,
            @RequestParam(required = false) String assignedTo,
            @RequestParam(required = false) String formKey) {
        
        List<Task> tasks;
        
        if (assignedTo != null && status != null) {
            tasks = taskService.findByAssignedToAndStatus(assignedTo, status);
        } else if (assignedTo != null) {
            tasks = taskService.findByAssignedTo(assignedTo);
        } else if (status != null) {
            tasks = taskService.findByStatus(status);
        } else if (formKey != null) {
            tasks = taskService.findByFormKey(formKey);
        } else {
            tasks = taskService.findByStatus(TaskStatus.CREATED); // Default to available tasks
        }
        
        List<TaskResponse> responses = tasks.stream()
                .map(TaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/available")
    public ResponseEntity<List<TaskResponse>> findAvailableTasks() {
        List<Task> tasks = taskService.findAvailableTasks();
        List<TaskResponse> responses = tasks.stream()
                .map(TaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{id}")
    public ResponseEntity<TaskResponse> findById(@PathVariable UUID id) {
        Task task = taskService.findById(id);
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/by-form-key")
    public ResponseEntity<TaskResponse> findByFormKeyAndProcessInstance(
            @RequestParam String formKey,
            @RequestParam UUID processInstanceId) {
        
        Task task = taskService.findByFormKeyAndProcessInstance(formKey, processInstanceId);
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<TaskResponse> update(
            @PathVariable UUID id,
            @Valid @RequestBody UpdateTaskRequest request) {
        
        Task task = taskService.update(
            id,
            request.getName(),
            request.getDescription(),
            request.getFormKey(),
            request.getOrder(),
            request.getInputs()
        );
        
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/assign")
    public ResponseEntity<TaskResponse> assign(
            @PathVariable UUID id,
            @RequestParam String assignedTo) {
        
        Task task = taskService.assign(id, assignedTo);
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/claim")
    public ResponseEntity<TaskResponse> claim(
            @PathVariable UUID id,
            @Valid @RequestBody ClaimTaskRequest request) {
        
        Task task = taskService.claim(id, request.getUsername());
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/complete")
    public ResponseEntity<TaskResponse> complete(
            @PathVariable UUID id,
            @Valid @RequestBody CompleteTaskRequest request) {
        
        Task task = taskService.complete(id, request.getUsername(), request.getOutputs());
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<TaskResponse> updateStatus(
            @PathVariable UUID id,
            @RequestParam TaskStatus status) {
        
        Task task = taskService.updateStatus(id, status);
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/inputs")
    public ResponseEntity<TaskResponse> updateInputs(
            @PathVariable UUID id,
            @RequestBody String inputs) {
        
        Task task = taskService.updateInputs(id, inputs);
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/outputs")
    public ResponseEntity<TaskResponse> updateOutputs(
            @PathVariable UUID id,
            @RequestBody String outputs) {
        
        Task task = taskService.updateOutputs(id, outputs);
        TaskResponse response = new TaskResponse(task);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable UUID id) {
        taskService.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/count")
    public ResponseEntity<Long> countByAssignedToAndStatus(
            @RequestParam String assignedTo,
            @RequestParam TaskStatus status) {
        
        long count = taskService.countByAssignedToAndStatus(assignedTo, status);
        return ResponseEntity.ok(count);
    }
}
