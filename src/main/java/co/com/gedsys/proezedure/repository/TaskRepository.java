package co.com.gedsys.proezedure.repository;

import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.domain.TaskStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TaskRepository extends JpaRepository<Task, UUID> {

    List<Task> findByProcessInstanceId(UUID processInstanceId);

    List<Task> findByProcessInstanceIdOrderByOrder(UUID processInstanceId);

    List<Task> findByAssignedTo(String assignedTo);

    List<Task> findByAssignedToAndStatus(String assignedTo, TaskStatus status);

    List<Task> findByStatus(TaskStatus status);

    List<Task> findByFormKey(String formKey);

    Optional<Task> findByFormKeyAndProcessInstanceId(String formKey, UUID processInstanceId);

    @Query("SELECT t FROM Task t WHERE t.assignedTo IS NULL AND t.status = :status")
    List<Task> findAvailableTasks(@Param("status") TaskStatus status);

    @Query("SELECT t FROM Task t WHERE t.processInstanceId = :processInstanceId AND t.status = :status ORDER BY t.order")
    List<Task> findByProcessInstanceIdAndStatusOrderByOrder(@Param("processInstanceId") UUID processInstanceId, 
                                                           @Param("status") TaskStatus status);

    @Query("SELECT t FROM Task t WHERE t.assignedTo = :assignedTo AND t.status IN :statuses ORDER BY t.createdAt DESC")
    List<Task> findByAssignedToAndStatusIn(@Param("assignedTo") String assignedTo, 
                                          @Param("statuses") List<TaskStatus> statuses);

    @Query("SELECT COUNT(t) FROM Task t WHERE t.processInstanceId = :processInstanceId AND t.status = :status")
    long countByProcessInstanceIdAndStatus(@Param("processInstanceId") UUID processInstanceId, 
                                          @Param("status") TaskStatus status);

    @Query("SELECT COUNT(t) FROM Task t WHERE t.assignedTo = :assignedTo AND t.status = :status")
    long countByAssignedToAndStatus(@Param("assignedTo") String assignedTo, 
                                   @Param("status") TaskStatus status);

    @Query("SELECT t FROM Task t WHERE t.processInstanceId = :processInstanceId AND t.order = " +
           "(SELECT MIN(t2.order) FROM Task t2 WHERE t2.processInstanceId = :processInstanceId AND t2.status != 'COMPLETED')")
    Optional<Task> findNextTaskInProcess(@Param("processInstanceId") UUID processInstanceId);

    boolean existsByFormKeyAndProcessInstanceId(String formKey, UUID processInstanceId);
}
