# Development Profile - PostgreSQL with Docker Compose Auto-Configuration
spring.docker.compose.enabled=true

# JPA Configuration for Development
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Flyway Configuration for Development
spring.flyway.enabled=true
spring.flyway.clean-disabled=false
spring.flyway.baseline-on-migrate=true

# Logging
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
