package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.Subtask;
import co.com.gedsys.proezedure.domain.SubtaskStatus;
import co.com.gedsys.proezedure.dto.CreateSubtaskRequest;
import co.com.gedsys.proezedure.dto.SubtaskResponse;
import co.com.gedsys.proezedure.service.SubtaskService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/tasks")
public class TaskSubtaskController {

    private final SubtaskService subtaskService;

    public TaskSubtaskController(SubtaskService subtaskService) {
        this.subtaskService = subtaskService;
    }

    @PostMapping("/{taskId}/subtasks")
    public ResponseEntity<SubtaskResponse> createSubtaskForTask(
            @PathVariable UUID taskId,
            @Valid @RequestBody CreateSubtaskRequest request) {
        
        // Override the taskId from path
        request.setTaskId(taskId);
        
        Subtask subtask = subtaskService.create(
            request.getTaskId(),
            request.getName(),
            request.getDescription(),
            request.getFormKey(),
            request.getOrder(),
            request.getInputs()
        );
        
        SubtaskResponse response = new SubtaskResponse(subtask);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/{taskId}/subtasks")
    public ResponseEntity<List<SubtaskResponse>> findSubtasksByTask(
            @PathVariable UUID taskId,
            @RequestParam(required = false) SubtaskStatus status) {
        
        List<Subtask> subtasks;
        
        if (status != null) {
            subtasks = subtaskService.findByTaskAndStatus(taskId, status);
        } else {
            subtasks = subtaskService.findByTask(taskId);
        }
        
        List<SubtaskResponse> responses = subtasks.stream()
                .map(SubtaskResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{taskId}/subtasks/next")
    public ResponseEntity<SubtaskResponse> findNextSubtask(@PathVariable UUID taskId) {
        Subtask nextSubtask = subtaskService.findNextSubtaskInTask(taskId);
        
        if (nextSubtask == null) {
            return ResponseEntity.noContent().build();
        }
        
        SubtaskResponse response = new SubtaskResponse(nextSubtask);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{taskId}/subtasks/count")
    public ResponseEntity<Long> countSubtasksByStatus(
            @PathVariable UUID taskId,
            @RequestParam SubtaskStatus status) {
        
        long count = subtaskService.countByTaskAndStatus(taskId, status);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/{taskId}/subtasks/all-completed")
    public ResponseEntity<Boolean> areAllSubtasksCompleted(@PathVariable UUID taskId) {
        boolean allCompleted = subtaskService.areAllSubtasksCompleted(taskId);
        return ResponseEntity.ok(allCompleted);
    }
}
